/**
 * voucher controller
 */

import { factories } from "@strapi/strapi";

const { createCoreController } = factories;

export default createCoreController("api::voucher.voucher", ({ strapi }) => ({
  async validateVoucher(ctx) {
    try {
      const { code, courseId, email } = ctx.request.body;

      if (!code || !courseId) {
        return ctx.badRequest("Cần có mã code và courseId");
      }

      // Tìm voucher theo mã (chuyển đổi thành chuỗi để xử lý cả số và chữ)
      const voucherCode = code.toString().toUpperCase();
      const voucher = await strapi.db.query("api::voucher.voucher").findOne({
        where: {
          code: voucherCode,
          time_start: { $lte: new Date() },
          time_end: { $gte: new Date() },
        },
        populate: ["courses"],
      });

      if (!voucher) {
        return ctx.badRequest("Không tìm thấy voucher hoặc đã hết hạn");
      }

      // Kiểm tra số lần sử dụng
      if (voucher.max_uses && voucher.current_uses >= voucher.max_uses) {
        console.log(
          `Voucher ${voucherCode} đã hết lượt sử dụng. Đã sử dụng: ${voucher.current_uses}/${voucher.max_uses}`
        );
        // Trả về response giống như khi voucher không hợp lệ
        return {
          valid: false,
          message: "Voucher đã hết lượt sử dụng",
        };
      }

      // Kiểm tra email có trong danh sách allowed_emails không (nếu có danh sách)
      if (
        email &&
        voucher.allowed_emails &&
        Array.isArray(voucher.allowed_emails) &&
        voucher.allowed_emails.length > 0
      ) {
        const isEmailAllowed = voucher.allowed_emails.some(
          (allowedEmail: string) =>
            allowedEmail.toLowerCase() === email.toLowerCase()
        );

        if (!isEmailAllowed) {
          return {
            valid: false,
            message: "Voucher không áp dụng cho email này",
          };
        }
      }

      // Kiểm tra email đã sử dụng voucher chưa
      if (email && voucher.used_emails && Array.isArray(voucher.used_emails)) {
        const isEmailUsed = voucher.used_emails.some(
          (usedEmail: string) => usedEmail.toLowerCase() === email.toLowerCase()
        );

        if (isEmailUsed) {
          return {
            valid: false,
            message: "Email này đã sử dụng voucher",
          };
        }
      }

      // Kiểm tra xem voucher có áp dụng cho khóa học này không
      const applicableToCourse = voucher.courses.some(
        (course: any) => course.id == courseId
      );
      if (!applicableToCourse) {
        return ctx.badRequest("Voucher không áp dụng cho khóa học này");
      }

      // Tính toán giá trị giảm giá
      let discountAmount = null;
      if (voucher.discount_amount && voucher.discount_amount > 0) {
        discountAmount = voucher.discount_amount;
      }

      return {
        valid: true,
        discount_percent: voucher.discount_percent,
        discount_amount: discountAmount,
        max_uses: voucher.max_uses,
        current_uses: voucher.current_uses,
        remaining_uses: voucher.max_uses
          ? voucher.max_uses - voucher.current_uses
          : null,
      };
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },

  // Tăng số lần sử dụng voucher
  async incrementVoucherUses(ctx) {
    try {
      const { code, userId, email } = ctx.request.body;

      if (!code) {
        return ctx.badRequest("Cần có mã voucher");
      }

      // Tìm voucher theo mã
      const voucherCode = code.toString().toUpperCase();
      const voucher = await strapi.db.query("api::voucher.voucher").findOne({
        where: { code: voucherCode },
      });

      if (!voucher) {
        return ctx.badRequest("Không tìm thấy voucher");
      }

      // Kiểm tra số lần sử dụng
      if (voucher.max_uses && voucher.current_uses >= voucher.max_uses) {
        console.log(
          `Voucher ${voucherCode} đã đạt giới hạn sử dụng. Đã sử dụng: ${voucher.current_uses}/${voucher.max_uses}`
        );
        // Trả về response thành công nhưng với thông báo đã đạt giới hạn
        return {
          success: false,
          message: "Voucher đã đạt giới hạn sử dụng",
          current_uses: voucher.current_uses,
          max_uses: voucher.max_uses,
          remaining_uses: 0,
        };
      }

      // Kiểm tra email có trong danh sách allowed_emails không (nếu có danh sách)
      if (
        email &&
        voucher.allowed_emails &&
        Array.isArray(voucher.allowed_emails) &&
        voucher.allowed_emails.length > 0
      ) {
        const isEmailAllowed = voucher.allowed_emails.some(
          (allowedEmail: string) =>
            allowedEmail.toLowerCase() === email.toLowerCase()
        );

        if (!isEmailAllowed) {
          return {
            success: false,
            message: "Voucher không áp dụng cho email này",
          };
        }
      }

      // Kiểm tra email đã sử dụng voucher chưa
      if (email && voucher.used_emails && Array.isArray(voucher.used_emails)) {
        const isEmailUsed = voucher.used_emails.some(
          (usedEmail: string) => usedEmail.toLowerCase() === email.toLowerCase()
        );

        if (isEmailUsed) {
          return {
            success: false,
            message: "Email này đã sử dụng voucher",
          };
        }
      }

      // Cập nhật danh sách email đã sử dụng
      let usedEmails = voucher.used_emails || [];
      if (email && !usedEmails.includes(email)) {
        usedEmails.push(email);
      }

      // Tăng số lần sử dụng
      const updatedVoucher = await strapi.db
        .query("api::voucher.voucher")
        .update({
          where: { id: voucher.id },
          data: {
            current_uses: (voucher.current_uses || 0) + 1,
            used_emails: usedEmails,
          },
        });

      const remainingUses = updatedVoucher.max_uses
        ? updatedVoucher.max_uses - updatedVoucher.current_uses
        : null;

      // Log thông tin chi tiết
      console.log(`
        Voucher ${voucherCode} đã được sử dụng.
        Thời gian: ${new Date().toISOString()}
        User ID: ${userId || "Không có"}
        Email: ${email || "Không có"}
        Số lần đã sử dụng: ${updatedVoucher.current_uses}/${updatedVoucher.max_uses || "Không giới hạn"}
        Số lần còn lại: ${remainingUses !== null ? remainingUses : "Không giới hạn"}
      `);

      return {
        success: true,
        current_uses: updatedVoucher.current_uses,
        max_uses: updatedVoucher.max_uses,
        remaining_uses: remainingUses,
      };
    } catch (error) {
      console.error("Error incrementing voucher uses:", error);
      return ctx.badRequest(error.message);
    }
  },
}));
