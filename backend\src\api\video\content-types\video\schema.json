{"kind": "collectionType", "collectionName": "videos", "info": {"singularName": "video", "pluralName": "videos", "displayName": "Video", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "url": {"type": "text"}, "thumbnail": {"type": "text"}, "date": {"type": "date"}, "description": {"type": "text"}, "video_categories": {"type": "relation", "relation": "manyToMany", "target": "api::video-category.video-category", "inversedBy": "videos"}}}