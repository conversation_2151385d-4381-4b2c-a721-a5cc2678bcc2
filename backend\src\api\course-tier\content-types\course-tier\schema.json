{"kind": "collectionType", "collectionName": "course_tiers", "info": {"singularName": "course-tier", "pluralName": "course-tiers", "displayName": "Course_tier", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"tier_name": {"type": "string"}, "price": {"type": "integer"}, "discord_role_id": {"type": "string"}, "is_default": {"type": "boolean"}, "course": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "course_tiers"}, "discord_channel_url": {"type": "text"}, "startDate": {"type": "date"}, "endDate": {"type": "date"}, "discount_percent": {"type": "integer"}, "discount_endDate": {"type": "date"}, "total_lessons": {"type": "integer"}, "orders": {"type": "relation", "relation": "oneToMany", "target": "api::order.order", "mappedBy": "course_tier"}, "tier_type": {"type": "enumeration", "enum": ["Trial", "Full"]}}}