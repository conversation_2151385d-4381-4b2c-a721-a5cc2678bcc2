"use client";

import React, { useEffect } from "react";

/**
 * VideoPopup component that displays a video in a modal popup
 * @param {Object} props Component props
 * @param {boolean} props.isOpen Controls if the popup is shown
 * @param {Function} props.onClose Function to call when closing the popup
 * @param {string} props.videoUrl URL of the video to display (YouTube embed URL)
 * @returns {JSX.Element} VideoPopup component
 */
const VideoPopup = ({ isOpen, onClose, videoUrl }) => {
  if (!isOpen) return null;

  // Preventing background scrolling when popup is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#000000] bg-opacity-80">
      <div className="relative w-full max-w-4xl mx-4">
        {/* Close button */}
        <div className="absolute -top-10 right-0">
          <button
            onClick={onClose}
            className="text-[#D5D7DA] hover:text-[#717680] underline"
          >
            Đóng
          </button>
        </div>

        {/* Video container */}
        <div className="relative w-full overflow-hidden rounded-lg aspect-video">
          <iframe
            className="absolute top-0 left-0 w-full h-full"
            src={videoUrl}
            title="Video Demo"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          ></iframe>
        </div>
      </div>
    </div>
  );
};

export default VideoPopup;
