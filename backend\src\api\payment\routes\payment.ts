export default {
  routes: [
    {
      method: 'POST',
      path: '/payments/create-payment-link',
      handler: 'payment.createPaymentLink',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/payments/verify',
      handler: 'payment.verifyPayment',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/payments/webhook',
      handler: 'payment.webhook',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/payments/complete',
      handler: 'payment.completePayment',
      config: {
        policies: [],
        middlewares: [],
      },
    }
  ],
  
};