"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";

/**
 * Component hiển thị dropdown thông báo khi người dùng click vào chuông thông báo
 * @param {Object} props
 * @param {boolean} props.isOpen - Trạng thái mở của dropdown
 * @param {Function} props.onClose - Hàm xử lý khi đóng dropdown
 * @param {Array} props.notifications - Danh sách thông báo (nếu có)
 * @returns {JSX.Element}
 */
const NotificationDropdown = ({ isOpen, onClose, notifications = [] }) => {
  // Tất cả các hooks phải được khai báo ở đầu component, không điều kiện
  const [isMobile, setIsMobile] = useState(false);

  // Hook useEffect cho việc kiểm tra kích thước màn hình
  useEffect(() => {
    // Chỉ thực hiện nếu dropdown đang mở
    if (!isOpen) return;

    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 960); // 960px là breakpoint cho tablet theo Tailwind
    };

    // Kiểm tra lúc mount component
    checkIfMobile();

    // Kiểm tra khi resize window
    window.addEventListener("resize", checkIfMobile);

    // Cleanup
    return () => window.removeEventListener("resize", checkIfMobile);
  }, [isOpen]);

  // Sau khi tất cả hooks đã được gọi, chúng ta có thể sử dụng điều kiện để return
  if (!isOpen) return null;

  // Xử lý khi click bên ngoài dropdown
  const handleOutsideClick = (e) => {
    if (e.target.id === "notification-backdrop") {
      onClose();
    }
  };

  return (
    <div
      id="notification-backdrop"
      className="fixed inset-0 z-50 bg-transparent"
      onClick={handleOutsideClick}
    >
      <div className="absolute top-16 right-4 w-[284px] h-[244px] bg-[#FFFFFF] rounded-2xl shadow-lg overflow-hidden border border-[#E9EAEB]">
        {/* Body của dropdown */}
        <div className="max-h-[400px] overflow-y-auto">
          {notifications.length > 0 ? (
            <div className="p-6">
              {/* Hiển thị danh sách thông báo khi có */}
              {notifications.map((notification, index) => (
                <div key={index} className="mb-4 last:mb-0">
                  {/* Chi tiết thông báo sẽ được thêm sau */}
                </div>
              ))}
            </div>
          ) : (
            // Hiển thị khi không có thông báo
            <div className="py-10 px-6 flex flex-col items-center justify-center">
              <div className="relative w-24 h-24 mb-6">
                <Image
                  src="/images/empty.png"
                  alt="Không có thông báo"
                  fill
                  className="object-contain"
                />
              </div>
              <p className="text-[#414651] text-base font-medium">
                Không có thông báo nào!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationDropdown;
