"use client";

import React, {useContext, useEffect, useRef, useState} from "react";
import {usePathname, useRouter} from "next/navigation";
import Image from "next/image";
import NavItem from "../dashboard/NavItem";
import Avatar from "../dashboard/Avatar";
import {UserContext} from "../../context/UserProvider";
import VideoIcon from "@/components/icons/VideoIcon";
import TaskIcon from "@/components/icons/TaskIcon";
import PracticeIcon from "@/components/icons/PracticeIcon";
import AchievementIcon from "@/components/icons/AchievementIcon";
import HomePageIcon from "@/components/icons/HomePageIcon";
import LineDivider from "@/components/icons/LineDivider";
import FireIcon from "@/components/icons/FireIcon";
import PointIcon from "@/components/icons/PointIcon";
import ArrowLeftIcon from "@/components/icons/ArrowLeftIcon";
import ExamIcon from "@/components/icons/ExamIcon";
import clsx from "clsx";
import AvatarIcon from "@/components/icons/AvatarIcon";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";

// Dashboard layout với sidebar và content area
// export default function DashboardLayout({title= "", keySearch, setKeySearch,children ,isTurnLive = false, isDetail = false, isSearch = false}) {
export default function DashboardLayout({children}) {

     // Lấy state từ context
    const {title, keySearch, setKeySearch,isSearch = false, isDetail = false, isTurnLive = false} = useDashboardLayout();

    const [collapsed, setCollapsed] = useState(false);
    const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
    const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =  useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [courseName, setCourseName] = useState("");
    const [planType, setPlanType] = useState("");
    const [course, setCourse] = useState({});
    const [isExpand, setIsExpand] = useState(true);
    const [isLive, setIsLive] = useState(false);
    const [streakNumber,setStreakNumber] = useState(0);
    const [chemPoint, setChemPoint] = useState(0);
    const pathname = usePathname();
    const {user, completedOrderInfo} = useContext(UserContext);
    const router = useRouter();
  
    const [widthSidebar, setWidthSidebar] = useState(0);
    const [widthScreen, setWidthScreen] = useState({gt1280: true,bw9611279: false,bw641960: false,bw375640: false});
    const sidebarRef = useRef(null);

    const headerRef = useRef(null);
    const [headerHeight, setHeaderHeight] = useState(0);

    const [menuItems, setMenuItems] = useState( [
        {name: "Trang chủ", path: "/quan-ly", icon: "homepage"},
        {name: "Xem video", path: "/quan-ly/xem-video", icon: "video"},
        {name: "Bài tập", path: "/quan-ly/bai-tap", icon: "task"},
        {name: "Bài thi", path: "/quan-ly/bai-thi", icon: "exam"},
        {name: "Khu tập luyện", path: "/quan-ly/khu-tap-luyen", icon: "practice"},
    ]);
    // Danh sách menu của trang quản trị

    const startDate = new Date('2025-06-03T23:30:00');
    const endDate = new Date('2025-06-04T21:30:00');

    useEffect(() => {
        function updateHeaderHeight() {
            if (headerRef.current) {
                setHeaderHeight(headerRef.current.offsetHeight);
            }
        }
        updateHeaderHeight();
        window.addEventListener("resize", updateHeaderHeight);
        return () => window.removeEventListener("resize", updateHeaderHeight);
    }, [isSearch, isTurnLive, isDetail, title]);

    useEffect( () => {
        const getCourse = async () => {

            const cookies = new Cookies();
            const user_data = cookies.get('user_data');
            const s = await strapi.users.getUserById(user_data.id);
            if (s.data.orders[0].documentId) {
                const instance = strapi.createStrapiClient();

                const response = await instance.get(`/orders/${s.data.orders[0].documentId}`, {
                    params: {
                        populate: {
                            course: true
                        },
                    },
                });

                if (response.data) {
                    const courseData = response.data.data.course;
                    
                    try {
                        // Fetch schedule data sử dụng strapi client instance để có authentication
                        const scheduleResponse = await instance.get('/class-schedules', {
                            params: {
                                'filters[courses][id][$eq]': courseData.id,
                            },
                        });

                        const scheduleData = scheduleResponse.data;
                        
                        // Safe access với optional chaining
                        const scheduleList = scheduleData?.data?.[0]?.schedule;

                        if (scheduleList?.length > 0) {
                            const [start, end, next] = findScheduleWithNext(scheduleList);
                            if (start && end) {
                                const now = new Date();
                                setIsLive(now >= start && now <= end);
                                
                                // Tạo object mới để trigger re-render
                                setCourse({
                                    ...courseData,
                                    start_date_live: start,
                                    end_date_live: end,
                                    next_date_live: next ?? start,
                                });
                                return; // Early return nếu có schedule
                            }
                        }
                        
                        // Log warning nếu không có schedule hợp lệ
                        if (process.env.NODE_ENV === 'development') {
                            console.warn('Không tìm thấy schedule hợp lệ cho course:', courseData.id);
                        }
                        
                    } catch (error) {
                        // Log error trong development
                        if (process.env.NODE_ENV === 'development') {
                            console.error("Error fetching or processing course schedule:", error);
                        }
                        
                        // TODO: Report to monitoring service in production
                        // reportError('getCourse', { courseId: courseData.id, error: error.message });
                    }
                    
                    // Fallback: Set course without schedule data
                    setCourse(courseData);
                }
            }
        }
        getCourse();
    }, []);

    const dayMap = {
        "Chủ nhật": 0,
        "Thứ 2": 1,
        "Thứ 3": 2,
        "Thứ 4": 3,
        "Thứ 5": 4,
        "Thứ 6": 5,
        "Thứ 7": 6
    };
    const parseSchedule = (item) => {
        try {
            // 1. Kiểm tra input cơ bản
            if (!item || typeof item !== 'string') {
                throw new Error('Input phải là string không rỗng');
            }

            // 2. Split và validate số phần tử
            const dayTimeParts = item.split(":");
            if (dayTimeParts.length !== 2) {
                throw new Error(`Format không đúng. Mong đợi "Ngày: Thời gian", nhận được: "${item}"`);
            }

            const [dayStr, timeRange] = dayTimeParts;

            // 3. Validate ngày
            if (!dayStr || !dayStr.trim()) {
                throw new Error('Thiếu thông tin ngày');
            }

            const weekday = dayMap[dayStr.trim()];
            if (weekday === undefined) {
                throw new Error(`Ngày không hợp lệ: "${dayStr.trim()}". Các ngày hợp lệ: ${Object.keys(dayMap).join(', ')}`);
            }

            // 4. Validate time range
            if (!timeRange || !timeRange.trim()) {
                throw new Error('Thiếu thông tin thời gian');
            }

            const timeParts = timeRange.trim().split(" - ");
            if (timeParts.length !== 2) {
                throw new Error(`Format thời gian không đúng. Mong đợi "HHhMM - HHhMM", nhận được: "${timeRange.trim()}"`);
            }

            const [startStr, endStr] = timeParts;

            // 5. Function helper để parse thời gian an toàn
            const parseTime = (timeStr, label) => {
                if (!timeStr || !timeStr.trim()) {
                    throw new Error(`Thiếu ${label}`);
                }

                const timeParts = timeStr.trim().split("h");
                if (timeParts.length === 0 || timeParts.length > 2) {
                    throw new Error(`Format ${label} không đúng: "${timeStr}". Mong đợi "HHh" hoặc "HHhMM"`);
                }

                const hour = parseInt(timeParts[0], 10);
                // Xử lý trường hợp "9h" (không có phút) và "9h00"
                const minute = timeParts.length === 2 ? parseInt(timeParts[1] || "0", 10) : 0;

                // Validate giá trị
                if (isNaN(hour) || hour < 0 || hour > 23) {
                    throw new Error(`Giờ ${label} không hợp lệ: ${hour}. Phải từ 0-23`);
                }
                if (isNaN(minute) || minute < 0 || minute > 59) {
                    throw new Error(`Phút ${label} không hợp lệ: ${minute}. Phải từ 0-59`);
                }

                return { hour, minute };
            };

            // 6. Parse thời gian bắt đầu và kết thúc
            const startTime = parseTime(startStr, 'thời gian bắt đầu');
            const endTime = parseTime(endStr, 'thời gian kết thúc');

            // 7. Validate logic thời gian
            const startTotalMinutes = startTime.hour * 60 + startTime.minute;
            const endTotalMinutes = endTime.hour * 60 + endTime.minute;
            
            if (startTotalMinutes >= endTotalMinutes) {
                throw new Error(`Thời gian bắt đầu (${startStr}) phải nhỏ hơn thời gian kết thúc (${endStr})`);
            }

            return {
                weekday,
                startHour: startTime.hour,
                startMinute: startTime.minute,
                endHour: endTime.hour,
                endMinute: endTime.minute
            };

        } catch (error) {
            // Chỉ log trong development environment
            if (process.env.NODE_ENV === 'development') {
                console.error(`❌ Lỗi parse schedule "${item}":`, error.message);
                console.warn('🔄 Sử dụng giá trị mặc định: Thứ 2, 9h00-10h00');
            }
            
            // TODO: Trong production, có thể gửi error lên monitoring service
            // như Sentry, LogRocket, hoặc custom analytics
            // reportError('parseSchedule', { item, error: error.message });
            
            // Trả về giá trị mặc định để tránh crash
            return {
                weekday: 1, // Thứ 2
                startHour: 9,
                startMinute: 0,
                endHour: 10,
                endMinute: 0
            };
        }
    }
    
    const calcNextDate = ({ weekday, hour, minute }, fromDate = new Date()) => {
        const now = new Date(fromDate);
        let diff = weekday - now.getDay();
        if (diff < 0 || (diff === 0 && (hour < now.getHours() || (hour === now.getHours() && minute <= now.getMinutes())))) {
            diff += 7;
        }
        const target = new Date(now);
        target.setDate(now.getDate() + diff);
        target.setHours(hour);
        target.setMinutes(minute);
        target.setSeconds(0);
        target.setMilliseconds(0);
        return target;
    }
    
    const findScheduleWithNext = (scheduleList) => {

        const now = new Date();

        const allOptions = scheduleList.map((item, index) => {
            const parsed = parseSchedule(item);
            const startDate = calcNextDate({ weekday: parsed.weekday, hour: parsed.startHour, minute: parsed.startMinute }, now);
            const endDate = new Date(startDate);
            endDate.setHours(parsed.endHour);
            endDate.setMinutes(parsed.endMinute);
            return { index, startDate, endDate };
        });

        allOptions.sort((a, b) => a.startDate - b.startDate);
        const current = allOptions[0];

        // 👉 Luôn lấy next lịch trong mảng, nếu cuối mảng thì vòng lại đầu
        const nextIndex = (current.index + 1) % scheduleList.length;
        const nextParsed = parseSchedule(scheduleList[nextIndex]);
        const baseDate = new Date(current.startDate);
        baseDate.setDate(baseDate.getDate() + 1); // Đảm bảo luôn đi về tương lai
        const next = calcNextDate(
            { weekday: nextParsed.weekday, hour: nextParsed.startHour, minute: nextParsed.startMinute },
            baseDate
        );

        return [current.startDate, current.endDate, next];
    }
    useEffect(() => {
        const interval = setInterval(() => {
            const now = new Date();
            setIsLive(now >= new Date(course.start_date_live) && now <= new Date(course.end_date_live));
        }, 1000);
        return () => {
            clearInterval(interval);
        }
    }, [course]);

    // Kiểm tra thiết bị là mobile/tablet hay desktop
    useEffect(() => {
        if (typeof window !== "undefined") {
            const checkIsMobile = () => {
                setIsMobile(window.innerWidth < 960);
                let menu = menuItems.filter((value) => value.path !== "/quan-ly/user");
                const newWidthScreen = {
                    gt1280: false,
                    bw9611279: false,
                    bw641960: false,
                    bw375640: false,
                };
                if (window.innerWidth >= 1280) {
                    newWidthScreen.gt1280 = true;
                }
                else if (window.innerWidth >= 961 && window.innerWidth <= 1279) {
                    newWidthScreen.bw9611279 = true;
                }
                else if (window.innerWidth >= 641 && window.innerWidth <= 960) {
                    newWidthScreen.bw641960 = true;
                    menu = [...menu,{name: "Tài khoản", path: "/quan-ly/tai-khoan", icon: "avatar"}];
                }
                else if ( window.innerWidth <= 640) {
                    newWidthScreen.bw375640 = true;
                    menu = [...menu,{name: "Tài khoản", path: "/quan-ly/tai-khoan", icon: "avatar"}];
                }
                setWidthScreen(newWidthScreen);
                setMenuItems(menu);
                setTimeout(() => {
                    const width = document.getElementById("sidebar")?.offsetWidth;
                    setWidthSidebar(width);
                },200)
            };

            checkIsMobile();
            window.addEventListener("resize", checkIsMobile);
            return () => {
                window.removeEventListener("resize", checkIsMobile);
            };
        }
    }, []);
    // Lấy thông tin khóa học từ cookies hoặc context
    useEffect(() => {
        try {
            // Ưu tiên lấy từ context
            if (completedOrderInfo) {
                const courseData = completedOrderInfo.course;
                const courseTierData = completedOrderInfo.course_tier;

                if (courseData?.title) {
                    setCourseName(courseData.title);
                }

                if (courseTierData?.tier_type) {
                    setPlanType(courseTierData.tier_type);
                }
            }
        } catch (error) {
            console.error("Lỗi khi đọc thông tin khóa học:", error);
        }
    }, [completedOrderInfo]);

    // Xử lý khi click vào Avatar
    const handleAvatarClick = () => {
        if (isMobile) {
        } else {
            setIsProfileModalOpen(true);
            setIsChangePasswordModalOpen(true);
        }
    };
    const expandMenu = () => {
        setIsExpand(!isExpand);
    }

    // Render icon dựa trên tên
    const renderIcon = (iconName,path) => {
        let a = pathname.split("/");
        let path_ = ""
        if (a.length > 2) {
            path_ = "/" + a[1] + "/" + a[2];
        }
        switch (iconName) {
            case "homepage":
                return <HomePageIcon isActive={pathname === path}></HomePageIcon>;
            case "video":
                return <VideoIcon isActive={path_ === path}></VideoIcon>;
            case "task":
                return <TaskIcon isActive={path_ === path}></TaskIcon>;
            case "practice":
                return <PracticeIcon isActive={path_ === path}></PracticeIcon>;
            case "exam":
                return <ExamIcon isActive={path_ === path}></ExamIcon>;
            case "achievement":
                return <AchievementIcon isActive={path_ === path}></AchievementIcon>;
            case "avatar":
                return <AvatarIcon isActive={path_ === path}></AvatarIcon>;
            default:
                return '';
        }
    };
    const clickBack = () => {
        router.push("/quan-ly/xem-video");
    }
    const goToLive = async () => {
        if (course.link_livestream) {
            window.open(course.link_livestream, "_blank");
        }
    }
    const convertDate = (date_) => {
        const utcDate = new Date(date_);
        const vietnamDate = utcDate.toLocaleString("vi-VN", {
            timeZone: "Asia/Ho_Chi_Minh",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false, // 24h format
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
        });
        const [time, date] = vietnamDate.split(" ");
        const [hour] = time.split(":");
        return `${hour}h - Ngày ${date}`;
    }

    return (
        <div className="flex h-screen bg-white">
            {/* Sidebar - menu */}
            {
                widthScreen.gt1280 ?
                    <div className='md:block hidden bg-[#FFFFFF] border-r border-[#E9EAEB] p-xl w-[240px]' ref={sidebarRef} id="sidebar">
                        <div className="flex flex-col h-full">
                            <div className="flex  items-center justify-between pb-xl ">
                                <div className={!isExpand ? 'hidden' : 'flex items-center justify-center'}>
                                    <Image
                                        src="/images/homepage/logo.png"
                                        alt="Ông Bà Dạy Hóa"
                                        width={87}
                                        height={32}
                                        className="py-2 cursor-pointer"
                                        onClick={() => {window.location.href = '/quan-ly'}}
                                    />
                                </div>
                            </div>
                            <LineDivider></LineDivider>
                            <div className="flex-1 mt-lg flex flex-col gap-2">
                                {menuItems.map((item) => (
                                    <NavItem
                                        key={item.path}
                                        href={item.path}
                                        icon={renderIcon(item.icon,item.path)}
                                        text={item.name}
                                        isExpand={true}
                                        isActive={pathname === item.path}
                                    />
                                ))}
                            </div>
                            <div className="flex items-center gap-md cursor-pointer hover:bg-[#FAFAFA] hover:text-[#198C43]" onClick={handleAvatarClick}>
                                <Avatar size="md"/>
                                {/*<p className="text-md leading-md font-semibold text-secondary-700">Tài khoản</p>*/}
                            </div>
                        </div>
                    </div> : ''
            }
            {
                widthScreen.bw9611279 ?
                    <div className='bg-[#FFFFFF] border-r border-[#E9EAEB] p-xl w-[76px]' ref={sidebarRef} id="sidebar">
                        <div className="flex flex-col h-full">
                            <div className="flex items-center text-center justify-between pb-xl ">
                                <div className='flex items-center justify-center w-full'>
                                    <Image
                                        src="/images/homepage/LogoOngBaSmall.png"
                                        alt="Ông Bà Dạy Hóa"
                                        width={32}
                                        height={32}
                                        className="py-2 cursor-pointer"
                                        onClick={() => {window.location.href = '/quan-ly'}}
                                    />
                                </div>
                            </div>
                            <LineDivider></LineDivider>
                            <div className="flex-1 mt-lg flex flex-col gap-2">
                                {menuItems.map((item) => (
                                    <NavItem
                                        key={item.path}
                                        href={item.path}
                                        icon={renderIcon(item.icon,item.path)}
                                        isExpand={false}
                                        text={item.name}
                                        isActive={pathname === item.path}
                                    />
                                ))}
                            </div>
                            <div className="flex items-center gap-md cursor-pointer hover:bg-[#FAFAFA] hover:text-[#198C43]" onClick={handleAvatarClick}>
                                <Avatar size="md" onClick={handleAvatarClick}/>
                            </div>
                        </div>
                    </div> : ''
            }


            {/* Main Content */}
            <div className="flex-1 overflow-auto">
                {/* Header */}
                <header
                    id="header"
                    ref={headerRef}
                    className={clsx(
                        'fixed top-0 right-0 bg-white',
                        // widthSidebar ? `left-[${widthSidebar}px] w-[calc(100%-${widthSidebar}px)]` : 'left-0',
                        'z-40  flex flex-col items-center justify-between h-fit')}
                    style={{
                        left: widthSidebar && (widthScreen.bw9611279 || widthScreen.gt1280) ? `${widthSidebar}px`: '0',
                        width: widthSidebar && (widthScreen.bw9611279 || widthScreen.gt1280) ? `calc(100% - ${widthSidebar}px)` : '100%'
                    }}
                >
                    {/*thông báo đến giờ học*/}
                    {
                        isTurnLive ?
                            <div className={clsx(
                                "w-full py-md px-xl bg-brand-gradient",
                                widthScreen.bw375640 ? 'h-[58px]' : 'h-[52px]'
                            )}>
                                {
                                    isLive ?
                                        <div className={clsx(
                                            "flex flex-row w-full h-full gap-[8px] items-center text-center",
                                            widthScreen.bw375640 ? 'justify-between' : 'justify-center'
                                        )}>
                                            <div className={clsx(
                                                "flex  h-full items-center ",
                                                widthScreen.bw375640? 'flex-col' : 'flex-row gap-[8px]'
                                            )}>
                                                <div className={clsx("flex flex-row h-full items-center gap-[8px]")}>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                                                            <path d="M3.16663 7.33333L3.16663 9.33333M5.83329 4.66667L5.83329 11.3333M11.1666 6V10M13.8333 4V12M8.49996 2L8.49996 14" stroke="#D92D20" strokeWidth="1.33" strokeLinecap="round"/>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm leading-sm font-medium text-error-primary-600">Buổi Live đang phát</p>
                                                    </div>
                                                </div>
                                                <div className={clsx("flex flex-row h-full items-center gap-[8px]")}>
                                                    <div className={clsx(widthScreen.bw375640 ? 'hidden' : '')}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="2" viewBox="0 0 3 2" fill="none">
                                                            <circle cx="1.5" cy="1" r="1" fill="#535862"/>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm leading-sm font-semibold text-secondary-700">{convertDate(course.start_date_live)}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="cursor-pointer ml-[8px] py-md px-lg h-[36px] w-[120px] gap-xs rounded-md border-[2px] border-skeuemorphic-gradient-border bg-[#5865F2] flex flex-row items-center justify-center" onClick={goToLive}>
                                                <div>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
                                                        <path d="M16.7772 1.75221C15.514 1.17262 14.1595 0.745597 12.7432 0.501026C12.7174 0.496306 12.6916 0.508102 12.6784 0.531694C12.5042 0.841535 12.3112 1.24575 12.1761 1.56346C10.6528 1.33541 9.1373 1.33541 7.64525 1.56346C7.51009 1.23869 7.31012 0.841535 7.13513 0.531694C7.12184 0.508889 7.09608 0.497093 7.07029 0.501026C5.6548 0.744816 4.30025 1.17183 3.0363 1.75221C3.02536 1.75693 3.01598 1.7648 3.00976 1.77501C0.440459 5.61349 -0.26338 9.35762 0.0819 13.0553C0.0834623 13.0734 0.0936175 13.0907 0.107679 13.1017C1.80283 14.3466 3.44487 15.1024 5.05642 15.6033C5.08221 15.6112 5.10954 15.6017 5.12595 15.5805C5.50717 15.0599 5.84698 14.511 6.13834 13.9337C6.15554 13.8999 6.13912 13.8598 6.10398 13.8465C5.56497 13.642 5.05173 13.3927 4.55803 13.1096C4.51898 13.0868 4.51585 13.0309 4.55178 13.0042C4.65567 12.9263 4.75959 12.8453 4.85879 12.7636C4.87674 12.7486 4.90175 12.7455 4.92286 12.7549C8.16628 14.2357 11.6777 14.2357 14.8828 12.7549C14.9039 12.7447 14.9289 12.7478 14.9477 12.7628C15.0469 12.8446 15.1508 12.9263 15.2555 13.0042C15.2914 13.0309 15.289 13.0868 15.25 13.1096C14.7563 13.3982 14.243 13.642 13.7033 13.8457C13.6681 13.859 13.6525 13.8999 13.6697 13.9337C13.9673 14.5102 14.3071 15.0591 14.6813 15.5797C14.6969 15.6017 14.725 15.6112 14.7508 15.6033C16.3702 15.1024 18.0122 14.3466 19.7074 13.1017C19.7222 13.0907 19.7316 13.0742 19.7332 13.0561C20.1464 8.78115 19.041 5.06773 16.8029 1.7758C16.7975 1.7648 16.7881 1.75693 16.7772 1.75221ZM6.6227 10.8038C5.6462 10.8038 4.8416 9.90732 4.8416 8.80633C4.8416 7.70535 5.6306 6.80885 6.6227 6.80885C7.62258 6.80885 8.41939 7.71322 8.40376 8.80633C8.40376 9.90732 7.61476 10.8038 6.6227 10.8038ZM13.208 10.8038C12.2315 10.8038 11.4269 9.90732 11.4269 8.80633C11.4269 7.70535 12.2159 6.80885 13.208 6.80885C14.2079 6.80885 15.0047 7.71322 14.9891 8.80633C14.9891 9.90732 14.2079 10.8038 13.208 10.8038Z" fill="white"/>
                                                    </svg>
                                                </div>
                                                <p className="text-sm leading-sm font-semibold text-[#FFFFFF]">Vào lớp</p>
                                            </div>
                                        </div>
                                        :
                                        <div className="flex flex-row justify-center w-full h-full gap-[8px] items-center text-center">
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                                                    <path d="M15.1667 5.95441C15.1667 5.55053 15.1667 5.34859 15.0868 5.25508C15.0175 5.17395 14.9135 5.13089 14.8072 5.13926C14.6846 5.14891 14.5418 5.2917 14.2562 5.57729L11.8333 8.00016L14.2562 10.423C14.5418 10.7086 14.6846 10.8514 14.8072 10.8611C14.9135 10.8694 15.0175 10.8264 15.0868 10.7452C15.1667 10.6517 15.1667 10.4498 15.1667 10.0459V5.95441Z" stroke="#535862" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round"/>
                                                    <path d="M1.83333 6.5335C1.83333 5.41339 1.83333 4.85334 2.05132 4.42552C2.24306 4.04919 2.54902 3.74323 2.92535 3.55148C3.35317 3.3335 3.91322 3.3335 5.03333 3.3335H8.63333C9.75343 3.3335 10.3135 3.3335 10.7413 3.55148C11.1176 3.74323 11.4236 4.04919 11.6153 4.42552C11.8333 4.85334 11.8333 5.41339 11.8333 6.5335V9.46683C11.8333 10.5869 11.8333 11.147 11.6153 11.5748C11.4236 11.9511 11.1176 12.2571 10.7413 12.4488C10.3135 12.6668 9.75343 12.6668 8.63333 12.6668H5.03333C3.91322 12.6668 3.35317 12.6668 2.92535 12.4488C2.54902 12.2571 2.24306 11.9511 2.05132 11.5748C1.83333 11.147 1.83333 10.5869 1.83333 9.46683V6.5335Z" stroke="#535862" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-sm leading-sm font-medium text-secondary-700">Buổi Live tiếp theo</p>
                                            </div>
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="3" height="2" viewBox="0 0 3 2" fill="none">
                                                    <circle cx="1.5" cy="1" r="1" fill="#535862"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-sm leading-sm font-semibold text-secondary-700">{course.next_date_live ? convertDate(course.next_date_live) : ''}</p>
                                            </div>
                                        </div>
                                }
                            </div> : ''
                    }
                    <div className={clsx(
                        "flex flex-col w-full items-center justify-between py-xl gap-y-xl",
                        widthScreen.bw375640 || widthScreen.bw641960? 'px-xl' : 'px-4xl'
                    )}>
                        {/*yTitle + chempoint*/}
                        <div className="flex flex-row gap-x-lg w-full">
                            {/*left title + back*/}
                            <div className="title flex-1">
                                <div className="title_text text-left flex flex-row items-center h-full min-w-[40px] gap-x-2 ">
                                    {
                                        !isDetail ?
                                            <p className="text-primary-900 text-display-xs leading-xs font-semibold">{title}</p>
                                            :
                                            <div className="h-full items-center flex flex-row gap-sm cursor-pointer" onClick={clickBack}>
                                                <ArrowLeftIcon></ArrowLeftIcon>
                                                <p className="cursor-pointer text-tertiary-fg text-sm font-semibold leading-sm">Quay về</p>
                                            </div>
                                    }
                                </div>
                            </div>
                            {/*right icon point*/}
                            {/*anhhn25 comment chưa chạy streak*/}
                            <div className="w-[140px] gap-[8px] flex flex-row">
                                <div className="item py-md pl-lg pr-xl gap-xs flex flex-row">
                                    <FireIcon></FireIcon>
                                    {
                                        streakNumber > 0 ?
                                            <p className="font-semibold text-secondary-700 text-md leading-md">{streakNumber}</p>
                                            :
                                            <p className="font-semibold text-utility-gray-400 text-md leading-md">{streakNumber}</p>
                                    }


                                </div>
                                <div className="item py-md pl-lg pr-xl gap-xs flex flex-row">
                                    <PointIcon></PointIcon>
                                    {
                                        chemPoint > 0 ?
                                            <p className="font-semibold text-secondary-700 text-md leading-md">{chemPoint}</p>
                                            :
                                            <p className="font-semibold text-utility-gray-400 text-md leading-md">{chemPoint}</p>
                                    }
                                </div>
                            </div>
                        </div>

                        {/*search*/}
                        {
                            isSearch ?
                                <div className="flex flex-col gap-[2px] w-full">
                                    <div className={clsx(
                                        "flex items-center gap-md",
                                        widthScreen.bw375640 ? 'w-full' : ' w-[340px]'
                                    )}>
                                        <div
                                            className="flex items-center self-stretch gap-3 py-md px-lg border border-[#D5D7DA] rounded-md bg-white shadow-xs w-full">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                                 fill="none">
                                                <path
                                                    d="M17.5 17.5L14.5834 14.5833M16.6667 9.58333C16.6667 13.4954 13.4954 16.6667 9.58333 16.6667C5.67132 16.6667 2.5 13.4954 2.5 9.58333C2.5 5.67132 5.67132 2.5 9.58333 2.5C13.4954 2.5 16.6667 5.67132 16.6667 9.58333Z"
                                                    stroke="#717680" strokeWidth="1.67" strokeLinecap="round"
                                                    strokeLinejoin="round"/>
                                            </svg>
                                            <input
                                                type="text"
                                                placeholder="Search"
                                                value={keySearch}
                                                onChange={(e) => setKeySearch(e.target.value)}
                                                className="w-full outline-none text-md line-clamp-1 leading-md font-normal text-placeholder"
                                            />
                                        </div>
                                    </div>
                                </div>
                                :
                                ''
                        }
                    </div>
                    <LineDivider></LineDivider>
                </header>


                {/* Page Content */}
                <main className='py-8 md:px-8 px-4' style={{ marginTop: `${headerHeight}px` }} >{children}</main>

                {/* Mobile/Tablet Bottom Navigation */}
                { (widthScreen.bw641960 || widthScreen.bw375640) && (
                    <nav className="fixed bottom-0 left-0 right-0 bg-[#FFFFFF] border-t border-[#E9EAEB] z-50 w-full ">
                        <div className="flex justify-around items-center p-4 max-w-[641px] mx-auto">
                            {menuItems.map((item) => (
                                <a
                                    key={item.path}
                                    href={item.path}
                                    className={`flex flex-col items-center justify-center  ${
                                        pathname === item.path ? "text-[#299D55]" : "text-[#717680]"
                                    }`}
                                >
                                    <div className="mb-1">
                                        {React.cloneElement(renderIcon(item.icon,item.path), {
                                            className: `${
                                                pathname === item.path
                                                    ? "text-[#299D55]"
                                                    : "text-[#717680]"
                                            }`,
                                        })}
                                    </div>
                                    {
                                        widthScreen.bw375640 ? '' :  <span className="text-xs font-medium">{item.name}</span>
                                    }

                                </a>
                            ))}
                        </div>
                    </nav>
                )}
            </div>
        </div>
    );
}
