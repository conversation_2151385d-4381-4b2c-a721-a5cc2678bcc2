'use strict';

module.exports = {
  routes: [
    {
      method: 'POST',
      path: '/api/password-reset/send-otp',  // Added /api prefix
      handler: 'password-reset.sendOTP',
      config: {
        auth: false,
        policies: [],
      }
    },
    {
      method: 'POST',
      path: '/api/password-reset/verify-otp',  // Added /api prefix
      handler: 'password-reset.verifyOTP',
      config: {
        auth: false,
        policies: [],
      }
    }
  ]
};