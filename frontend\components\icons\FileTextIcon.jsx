"use client";

import React from "react";
import {CommonUtil} from "@/utils/CommonUtil";


const FileTextIcon = ({
                            width = 20,
                           height = 20,
                           stroke = "#535862",
                           className = "",
                       }) =>
{
    width = Number(width) || 20;
    height = Number(height) || 20;
    let viewBox = `0 0 ${width} ${height}`;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox={viewBox}
            fill="none"
            className={className}
        >
            <path
                d="M11.6666 1.89127V5.33338C11.6666 5.80009 11.6666 6.03345 11.7574 6.21171C11.8373 6.36851 11.9648 6.49599 12.1216 6.57589C12.2999 6.66672 12.5332 6.66672 12.9999 6.66672H16.442M13.3333 10.8333H6.66658M13.3333 14.1667H6.66658M8.33325 7.49999H6.66658M11.6666 1.66666H7.33325C5.93312 1.66666 5.23305 1.66666 4.69828 1.93914C4.22787 2.17882 3.84542 2.56128 3.60574 3.03168C3.33325 3.56646 3.33325 4.26653 3.33325 5.66666V14.3333C3.33325 15.7335 3.33325 16.4335 3.60574 16.9683C3.84542 17.4387 4.22787 17.8212 4.69828 18.0608C5.23305 18.3333 5.93312 18.3333 7.33325 18.3333H12.6666C14.0667 18.3333 14.7668 18.3333 15.3016 18.0608C15.772 17.8212 16.1544 17.4387 16.3941 16.9683C16.6666 16.4335 16.6666 15.7335 16.6666 14.3333V6.66666L11.6666 1.66666Z"
                stroke={stroke}
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    )
}


export default FileTextIcon;
