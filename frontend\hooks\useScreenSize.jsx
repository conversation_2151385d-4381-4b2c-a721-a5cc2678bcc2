import { useState, useEffect } from "react";

export function useScreenSize() {
    const [size, setSize] = useState(null);

    useEffect(() => {
        function handleResize() {
            const width = window.innerWidth;
            const flags = {
                gt1280: false,
                bw9611279: false,
                bw641960: false,
                bw375640: false,
                lte960: false
            };
            if (width >= 1280) flags.gt1280 = true;
            else if (width >= 961 && width <= 1279) flags.bw9611279 = true;
            else if (width >= 641 && width <= 960) flags.bw641960 = true;
            else if (width <= 640) flags.bw375640 = true;

            if (width <= 960)  flags.lte960 = true;
            setSize(flags);
        }

        window.addEventListener("resize", handleResize);
        handleResize(); // call once

        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return size;
}
