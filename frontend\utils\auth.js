// 'use client'

// import { useCookies } from 'react-cookie'
// import { useRouter } from 'next/navigation'

// // Hook để lấy thông tin user hiện tại
// export function useUser() {
//     const [cookies] = useCookies(['user_data'])
//     const userData = cookies.user_data ? JSON.parse(cookies.user_data) : null

//     return { user: userData, isLoggedIn: !!userData }
// }

// // Hook đăng nhập
// export function useLogin() {
//     const router = useRouter()

//     const login = async (credentials) => {
//         try {
//             const response = await fetch('/api/auth/login', {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json'
//                 },
//                 body: JSON.stringify(credentials)
//             })

//             const data = await response.json()

//             if (data.success) {
//                 // Reload trang để cập nhật cookies và state
//                 router.refresh()

//                 // Chuyển hướng sau khi đăng nhập thành công
//                 if (credentials.callbackUrl) {
//                     router.push(credentials.callbackUrl)
//                 } else {
//                     router.push('/')
//                 }

//                 return { success: true, user: data.user }
//             } else {
//                 return { success: false, error: data.error || 'Đăng nhập thất bại' }
//             }
//         } catch (error) {
//             console.error('Lỗi đăng nhập:', error)
//             return { success: false, error: 'Đăng nhập thất bại. Vui lòng thử lại sau.' }
//         }
//     }

//     return { login }
// }

// // Hook đăng xuất
// export function useLogout() {
//     const router = useRouter()

//     const logout = async () => {
//         try {
//             await fetch('/api/auth/logout', {
//                 method: 'POST'
//             })

//             // Reload trang để cập nhật cookies và state
//             router.refresh()

//             // Chuyển hướng về trang đăng nhập
//             router.push('/dang-nhap')

//             return { success: true }
//         } catch (error) {
//             console.error('Lỗi đăng xuất:', error)
//             return { success: false, error: 'Đăng xuất thất bại. Vui lòng thử lại sau.' }
//         }
//     }

//     return { logout }
// } 