"use client";

import Link from "next/link";
import React, {cloneElement} from "react";
import clsx from "clsx";

const NavItem = ({href, icon, text, isActive = false, isExpand = false}) => {
    // Thêm màu sắc cho icon khi hover/active
    const styledIcon = React.isValidElement(icon) ? cloneElement(icon, {
        className: `${isActive ? "text-[#198C43]" : "text-[#414651] group-hover:text-[#198C43]"}`,
        stroke: isActive ? "currentColor" : "inherit",
    }) : icon;

    return (
        <Link
            href={href}
            className={clsx(
                "flex items-center px-3 py-2 rounded-lg gap-2 transition-colors duration-200 group",
                isActive ? "text-[#198C43] hover:bg-[#FAFAFA]" : "text-[#414651] hover:bg-[#FAFAFA] hover:text-[#198C43]"
            )}
        >
            <div className="flex items-center gap-2 relative">
                <span className={clsx(
                    isActive ? "text-[#198C43]" : "text-[#414651] group-hover:text-[#198C43]"
                )}>
                    {styledIcon}
                </span>
                <span className="font-medium">{ isExpand ? text : ''}</span>
                {/* Tooltip */}
                {
                    !isExpand ?
                        <div
                            className="absolute left-full top-1/2 -translate-y-1/2 ml-2 hidden items-center px-2 py-1 bg-brand-solid h-[34px]
                                rounded-md shadow-lg transition-all duration-200 whitespace-nowrap group-hover:flex"
                            style={{zIndex: 90}}
                        >
                            <div className="absolute -left-[8px] top-1/2 -translate-y-1/2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="9" height="16" viewBox="0 0 9 16" fill="none">
                                    <path d="M8.51471 14.0711C8.51471 14.962 7.43757 15.4081 6.8076 14.7782L0.736529 8.7071C0.346004 8.31658 0.346005 7.68342 0.736529 7.29289L6.8076 1.22183C7.43757 0.591867 8.51471 1.03803 8.51471 1.92894L8.51471 14.0711Z" fill="#299D55"/>
                                </svg>
                            </div>
                            <p className="font-normal text-white text-xs leading-xs">{text}</p>
                        </div>
                        :
                        ''
                }

            </div>
        </Link>
    );
};

export default NavItem;
