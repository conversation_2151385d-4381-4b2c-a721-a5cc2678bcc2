import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const FireIcon = ({
                      width = 24,
                      height = 24,
                      stroke = "#535862",
                      className = '', ...props }) => {

    let viewBox = `0 0 ${width} ${height}`;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    return (

        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox={viewBox}
            fill="none"
            className={className}
            {...props}
        >
            <path
                d="M17.2499 15.1718C17.2499 18 15.571 20.2927 13.4999 20.2927C11.4288 20.2927 9.74992 18 9.74992 15.1718C9.74992 12.3435 11.4288 10.0508 13.4999 10.0508C15.571 10.0508 17.2499 12.3435 17.2499 15.1718Z"
                fill="#FFCE51"
            />
            <path
                d="M19.48 12.5873C17.91 8.60758 12.32 8.39298 13.67 2.60871C13.77 2.17952 13.3 1.84788 12.92 2.07223C9.29002 4.15964 6.68002 8.34421 8.87002 13.8261C9.05002 14.2748 8.51002 14.6942 8.12002 14.4016C6.31002 13.0653 6.12003 11.1437 6.28002 9.76833C6.34002 9.26111 5.66003 9.01725 5.37003 9.43669C4.69003 10.4511 4.00003 12.0898 4.00003 14.5577C4.38003 20.02 9.11002 21.6978 10.81 21.9124C13.24 22.2148 15.87 21.7758 17.76 20.0883C19.84 18.2058 20.6 15.2014 19.48 12.5873ZM10.2 17.4937C11.64 17.1523 12.38 16.1379 12.58 15.2405C12.91 13.8456 11.62 12.48 12.49 10.2756C12.82 12.0996 15.76 13.2408 15.76 15.2307C15.84 17.6985 13.1 19.8152 10.2 17.4937Z"
                fill="#FF7324"
            />
        </svg>
    );
}

export default FireIcon;
