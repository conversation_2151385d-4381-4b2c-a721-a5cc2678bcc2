{"kind": "collectionType", "collectionName": "blog_posts", "info": {"singularName": "blog-post", "pluralName": "blog-posts", "displayName": "Blog-Post", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "slug": {"type": "uid", "targetField": "title"}, "description": {"type": "text"}, "category": {"type": "enumeration", "enum": ["<PERSON><PERSON>", "Mẹo giải đề", "<PERSON><PERSON><PERSON>"]}, "readTime": {"type": "string"}, "date": {"type": "datetime"}, "displayDate": {"type": "string"}, "featuredImage": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "sections": {"type": "relation", "relation": "oneToMany", "target": "api::blog-content-section.blog-content-section", "mappedBy": "blogPost"}}}