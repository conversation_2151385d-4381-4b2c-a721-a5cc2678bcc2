import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const TaskIcon = ({width = 20, height = 20, isActive = false}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;

    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    if (!isActive) {
        return (

            <svg xmlns="http://www.w3.org/2000/svg"
                 width={width}
                 height={height}
                 viewBox={viewBox}
            >
                <path
                    strokeWidth={strokeWidth}
                    fill="currentColor"
                    d="M14.9983 8.33317C14.9983 8.79433 15.3722 9.16817 15.8333 9.16817C16.2945 9.16817 16.6683 8.79433 16.6683 8.33317H14.9983ZM7.5 19.1682C7.96116 19.1682 8.335 18.7943 8.335 18.3332C8.335 17.872 7.96116 17.4982 7.5 17.4982V19.1682ZM2.77248 16.9681L3.51647 16.5891L3.51647 16.5891L2.77248 16.9681ZM3.86502 18.0607L3.48594 18.8047H3.48594L3.86502 18.0607ZM14.4683 1.93899L14.8474 1.195V1.195L14.4683 1.93899ZM15.5608 3.03153L14.8169 3.41061L14.8169 3.41061L15.5608 3.03153ZM3.86502 1.93899L4.24411 2.68298L3.86502 1.93899ZM2.77248 3.03153L3.51647 3.41061V3.41061L2.77248 3.03153ZM10.8333 10.0015C11.2945 10.0015 11.6683 9.62766 11.6683 9.1665C11.6683 8.70535 11.2945 8.3315 10.8333 8.3315V10.0015ZM5.83333 8.3315C5.37218 8.3315 4.99833 8.70535 4.99833 9.1665C4.99833 9.62766 5.37218 10.0015 5.83333 10.0015V8.3315ZM7.5 13.3348C7.96116 13.3348 8.335 12.961 8.335 12.4998C8.335 12.0387 7.96116 11.6648 7.5 11.6648V13.3348ZM5.83333 11.6648C5.37218 11.6648 4.99833 12.0387 4.99833 12.4998C4.99833 12.961 5.37218 13.3348 5.83333 13.3348V11.6648ZM12.5 6.66817C12.9612 6.66817 13.335 6.29433 13.335 5.83317C13.335 5.37201 12.9612 4.99817 12.5 4.99817V6.66817ZM5.83333 4.99817C5.37218 4.99817 4.99833 5.37201 4.99833 5.83317C4.99833 6.29433 5.37218 6.66817 5.83333 6.66817V4.99817ZM14.8101 11.98L14.2754 11.3387L14.2434 11.3654L14.2142 11.3951L14.8101 11.98ZM11.4582 15.3758L12.1359 15.8636L12.2932 15.6451V15.3758H11.4582ZM11.021 16.8342L11.8399 16.9972L11.844 16.9765L11.8471 16.9555L11.021 16.8342ZM12.52 18.105L12.431 17.2747L12.3905 17.2791L12.3506 17.2873L12.52 18.105ZM13.9149 17.6258L14.4242 18.2875L14.4469 18.27L14.4683 18.2511L13.9149 17.6258ZM17.3085 14.2092L17.9348 14.7614L17.9597 14.7331L17.982 14.7027L17.3085 14.2092ZM17.3585 12.9592L16.5969 13.3016L16.6125 13.3363L16.6312 13.3695L17.3585 12.9592ZM16.3091 11.855L16.8381 11.2089L16.7911 11.1704L16.7389 11.1391L16.3091 11.855ZM6.5 1.6665V2.5015H11.8333V1.6665V0.831504H6.5V1.6665ZM2.5 14.3332H3.335V5.6665H2.5H1.665V14.3332H2.5ZM15.8333 5.6665H14.9983V8.33317H15.8333H16.6683V5.6665H15.8333ZM7.5 18.3332V17.4982H6.5V18.3332V19.1682H7.5V18.3332ZM2.5 14.3332H1.665C1.665 15.0195 1.66435 15.5844 1.70183 16.0432C1.74011 16.5117 1.82228 16.9425 2.02849 17.3472L2.77248 16.9681L3.51647 16.5891C3.45021 16.459 3.39613 16.2724 3.36629 15.9072C3.33565 15.5322 3.335 15.047 3.335 14.3332H2.5ZM6.5 18.3332V17.4982C5.78616 17.4982 5.30099 17.4975 4.926 17.4669C4.56076 17.437 4.37416 17.383 4.24411 17.3167L3.86502 18.0607L3.48594 18.8047C3.89066 19.0109 4.32149 19.0931 4.79001 19.1313C5.24878 19.1688 5.81371 19.1682 6.5 19.1682V18.3332ZM2.77248 16.9681L2.02849 17.3472C2.34823 17.9748 2.85842 18.4849 3.48594 18.8047L3.86502 18.0607L4.24411 17.3167C3.93082 17.1571 3.6761 16.9024 3.51647 16.5891L2.77248 16.9681ZM11.8333 1.6665V2.5015C12.5472 2.5015 13.0323 2.50215 13.4073 2.53279C13.7726 2.56263 13.9592 2.61671 14.0892 2.68298L14.4683 1.93899L14.8474 1.195C14.4427 0.988782 14.0118 0.906617 13.5433 0.868338C13.0846 0.830855 12.5196 0.831504 11.8333 0.831504V1.6665ZM15.8333 5.6665H16.6683C16.6683 4.98022 16.669 4.41528 16.6315 3.95651C16.5932 3.488 16.5111 3.05717 16.3048 2.65245L15.5608 3.03153L14.8169 3.41061C14.8831 3.54067 14.9372 3.72726 14.967 4.0925C14.9977 4.4675 14.9983 4.95266 14.9983 5.6665H15.8333ZM14.4683 1.93899L14.0892 2.68298C14.4025 2.84261 14.6572 3.09732 14.8169 3.41061L15.5608 3.03153L16.3048 2.65245C15.9851 2.02492 15.4749 1.51473 14.8474 1.195L14.4683 1.93899ZM6.5 1.6665V0.831504C5.81371 0.831504 5.24878 0.830855 4.79001 0.868338C4.32149 0.906617 3.89066 0.988782 3.48594 1.195L3.86502 1.93899L4.24411 2.68298C4.37416 2.61671 4.56076 2.56263 4.926 2.53279C5.30099 2.50215 5.78616 2.5015 6.5 2.5015V1.6665ZM2.5 5.6665H3.335C3.335 4.95266 3.33565 4.4675 3.36629 4.0925C3.39613 3.72726 3.45021 3.54067 3.51647 3.41061L2.77248 3.03153L2.02849 2.65245C1.82228 3.05717 1.74011 3.488 1.70183 3.95651C1.66435 4.41528 1.665 4.98022 1.665 5.6665H2.5ZM3.86502 1.93899L3.48594 1.195C2.85842 1.51473 2.34823 2.02493 2.02849 2.65245L2.77248 3.03153L3.51647 3.41061C3.6761 3.09732 3.93082 2.84261 4.24411 2.68298L3.86502 1.93899ZM10.8333 9.1665V8.3315H5.83333V9.1665V10.0015H10.8333V9.1665ZM7.5 12.4998V11.6648H5.83333V12.4998V13.3348H7.5V12.4998ZM12.5 5.83317V4.99817H5.83333V5.83317V6.66817H12.5V5.83317ZM14.8101 11.98L14.2142 11.3951C13.648 11.9719 12.8223 12.7724 12.1186 13.466C11.7711 13.8086 11.4522 14.1262 11.2187 14.3694C11.1033 14.4895 10.9992 14.6013 10.9206 14.6933C10.8828 14.7375 10.8381 14.7919 10.7986 14.8485C10.7796 14.8757 10.7494 14.9209 10.7208 14.9771C10.708 15.0021 10.6232 15.16 10.6232 15.3758H11.4582H12.2932C12.2932 15.5826 12.2127 15.7275 12.2084 15.736C12.1881 15.7757 12.1705 15.8008 12.1679 15.8044C12.1613 15.8139 12.1651 15.8075 12.19 15.7785C12.2365 15.724 12.3138 15.6401 12.4232 15.5262C12.6392 15.3012 12.9436 14.9978 13.2909 14.6554C13.9769 13.9792 14.8271 13.1547 15.406 12.5649L14.8101 11.98ZM11.4582 15.3758L10.7805 14.8881C10.6137 15.1198 10.4981 15.4076 10.4125 15.6906C10.3235 15.9855 10.251 16.3304 10.1948 16.7128L11.021 16.8342L11.8471 16.9555C11.895 16.6296 11.9522 16.369 12.0112 16.1736C12.0405 16.0764 12.0685 16.0017 12.0928 15.9467C12.1048 15.9196 12.1151 15.8993 12.1231 15.8849C12.127 15.8778 12.1302 15.8725 12.1325 15.8689C12.1348 15.8652 12.136 15.8636 12.1359 15.8636L11.4582 15.3758ZM11.021 16.8342L10.202 16.6711C10.1455 16.9551 10.0607 17.3246 10.0234 17.6062C10.0041 17.752 9.9878 17.9357 10.0069 18.1199C10.0245 18.2902 10.0864 18.6034 10.3551 18.8477L10.9169 18.23L11.4787 17.6123C11.5684 17.6939 11.616 17.7817 11.6399 17.8421C11.6626 17.8996 11.6672 17.94 11.668 17.9479C11.669 17.9574 11.6657 17.9256 11.6789 17.8254C11.6913 17.7323 11.7125 17.6174 11.742 17.4721C11.7681 17.3439 11.8082 17.1563 11.8399 16.9972L11.021 16.8342ZM10.9169 18.23L10.3551 18.8477C10.6154 19.0845 10.9325 19.1233 11.0774 19.1345C11.2573 19.1485 11.4446 19.1352 11.6095 19.1161C11.9406 19.0778 12.3352 18.996 12.6893 18.9226L12.52 18.105L12.3506 17.2873C11.9748 17.3652 11.6594 17.4292 11.4174 17.4572C11.2956 17.4713 11.2304 17.4714 11.2066 17.4695C11.1962 17.4687 11.2173 17.4691 11.2573 17.4816C11.2959 17.4937 11.3852 17.5272 11.4787 17.6123L10.9169 18.23ZM12.52 18.105L12.609 18.9352C13.2954 18.8616 13.9974 18.616 14.4242 18.2875L13.9149 17.6258L13.4056 16.9641C13.3626 16.9972 13.2468 17.0652 13.0551 17.1332C12.8719 17.1983 12.6546 17.2508 12.431 17.2747L12.52 18.105ZM13.9149 17.6258L14.4683 18.2511C14.8922 17.8759 17.6021 15.1387 17.9348 14.7614L17.3085 14.2092L16.6821 13.6569C16.3903 13.9879 13.7287 16.6755 13.3614 17.0006L13.9149 17.6258ZM17.3085 14.2092L17.982 14.7027C18.2241 14.3723 18.3156 13.9701 18.3286 13.632C18.3419 13.29 18.2798 12.8929 18.0857 12.5488L17.3585 12.9592L16.6312 13.3695C16.6308 13.3687 16.6333 13.373 16.6372 13.3838C16.641 13.3944 16.6454 13.4094 16.6494 13.4287C16.6576 13.4683 16.6619 13.5165 16.6599 13.5675C16.6579 13.6188 16.65 13.6623 16.6407 13.6934C16.631 13.7262 16.6244 13.73 16.6349 13.7156L17.3085 14.2092ZM17.3585 12.9592L18.12 12.6167C18.0379 12.4342 17.8998 12.2657 17.8147 12.1658C17.7102 12.0433 17.5881 11.9143 17.4702 11.7955C17.2377 11.5611 16.9817 11.3265 16.8381 11.2089L16.3091 11.855L15.7801 12.5011C15.8656 12.571 16.078 12.7634 16.2848 12.9718C16.3866 13.0744 16.4768 13.1706 16.5439 13.2493C16.6302 13.3505 16.6228 13.3591 16.5969 13.3016L17.3585 12.9592ZM16.3091 11.855L16.7389 11.1391C16.0625 10.733 15.0859 10.6628 14.2754 11.3387L14.8101 11.98L15.3449 12.6213C15.4545 12.5299 15.5492 12.5048 15.6242 12.5018C15.7077 12.4985 15.798 12.5221 15.8793 12.5709L16.3091 11.855Z"/>
            </svg>
        );
    }else {
        return (
            <svg xmlns="http://www.w3.org/2000/svg"
                 width={width}
                 height={height}
                 viewBox={viewBox} fill="#299D55">
                <path fillRule="evenodd" clipRule="evenodd"  strokeWidth={strokeWidth} d="M11.8335 0.833254C12.5197 0.833253 13.0846 0.832403 13.5433 0.869875C14.0116 0.908148 14.4426 0.990154 14.847 1.19621L15.0765 1.32479C15.5985 1.64503 16.024 2.1042 16.3037 2.65291L16.3753 2.80591C16.5296 3.16568 16.5965 3.54676 16.63 3.95663C16.6675 4.41527 16.6666 4.98019 16.6666 5.66642V9.31144C15.6805 9.03394 14.5112 9.12648 13.4708 9.85913L13.4432 9.87866L13.4163 9.89982L13.2625 10.0178L13.2405 10.0341L13.2194 10.0512L13.1152 10.1366L13.0501 10.2042L13.0265 10.2286C12.7497 10.5106 12.4105 10.8458 12.0507 11.1995L10.9505 12.2802C10.7768 12.4514 10.6058 12.6198 10.4476 12.7782L10.0179 13.2161C9.95676 13.2797 9.8938 13.3463 9.83232 13.413L9.65572 13.6116C9.6332 13.6379 9.59925 13.6771 9.56051 13.7255L9.43355 13.8948C9.40509 13.9356 9.35329 14.0164 9.29358 14.1218C9.1196 14.4043 8.9935 14.6939 8.90214 14.9527L8.81913 15.2083C8.70855 15.5743 8.62632 15.9706 8.5636 16.3744C8.53118 16.5342 8.46656 16.8277 8.41712 17.1077L8.37317 17.3876C8.34829 17.5755 8.31112 17.9125 8.35038 18.2917L8.3691 18.4382C8.3979 18.6265 8.45694 18.8851 8.58069 19.1666H6.4998C5.81356 19.1666 5.24865 19.1674 4.79 19.13C4.38013 19.0965 3.99906 19.0296 3.63928 18.8752L3.48629 18.8036C2.93757 18.524 2.4784 18.0985 2.15816 17.5764L2.02958 17.3469C1.82353 16.9425 1.74152 16.5116 1.70325 16.0432C1.66578 15.5846 1.66663 15.0196 1.66663 14.3334V5.66642C1.66663 4.98019 1.66578 4.41527 1.70325 3.95663C1.74152 3.48825 1.82352 3.05733 2.02958 2.65291L2.15816 2.42342C2.4784 1.90134 2.93757 1.47583 3.48629 1.19621L3.63928 1.12459C3.99906 0.970272 4.38013 0.903368 4.79 0.869875C5.24865 0.832402 5.81356 0.833253 6.4998 0.833254H11.8335ZM5.83329 11.6666C5.37307 11.6666 4.99997 12.0397 4.99996 12.4999C4.99996 12.9602 5.37307 13.3332 5.83329 13.3333H7.49996C7.9602 13.3333 8.33329 12.9602 8.33329 12.4999C8.33328 12.0397 7.96019 11.6666 7.49996 11.6666H5.83329ZM5.83329 8.33325C5.37307 8.33326 4.99997 8.70636 4.99996 9.16659C4.99996 9.62682 5.37307 9.99991 5.83329 9.99992H10.8333C11.2935 9.99992 11.6666 9.62682 11.6666 9.16659C11.6666 8.70636 11.2935 8.33325 10.8333 8.33325H5.83329ZM5.83329 4.99992C5.37307 4.99993 4.99997 5.37303 4.99996 5.83325C4.99996 6.29348 5.37307 6.66658 5.83329 6.66659H12.5C12.9602 6.66659 13.3333 6.29349 13.3333 5.83325C13.3333 5.37302 12.9602 4.99992 12.5 4.99992H5.83329Z" fill="#299D55"/>
                <path  strokeWidth={strokeWidth} d="M14.4303 11.2223C15.1555 10.7115 15.9855 10.7516 16.608 11.0684L16.7382 11.1409L16.7903 11.1718L16.8367 11.21C16.9802 11.3275 17.2365 11.5625 17.469 11.7968C17.5869 11.9156 17.7089 12.0446 17.8133 12.1671C17.8876 12.2542 18.0002 12.3944 18.0826 12.5496H18.0843C18.2782 12.8932 18.34 13.2901 18.3268 13.6319C18.3153 13.9275 18.2444 14.2725 18.0647 14.5751L17.9809 14.7021L17.9581 14.7322L17.9337 14.7607C17.5999 15.1391 14.8906 17.8752 14.4669 18.2502L14.4458 18.269L14.423 18.2861C14.0065 18.6065 13.3275 18.8468 12.657 18.9265C12.3113 18.9981 11.93 19.0773 11.6088 19.1145C11.4442 19.1335 11.2569 19.1471 11.0774 19.1332C10.9325 19.1219 10.6161 19.0828 10.3564 18.8468C10.0885 18.603 10.0258 18.2902 10.0081 18.12C9.98906 17.9361 10.0059 17.7523 10.0252 17.6065C10.0619 17.3293 10.1444 16.9667 10.201 16.6845C10.2565 16.3137 10.3274 15.9782 10.4142 15.6908C10.4889 15.4435 10.5871 15.1917 10.7218 14.9779C10.7504 14.922 10.7809 14.8766 10.7999 14.8494C10.8393 14.793 10.8843 14.7388 10.922 14.6947C11.0005 14.6029 11.1046 14.4908 11.2198 14.3709C11.4533 14.1278 11.7725 13.81 12.1199 13.4675C12.8235 12.774 13.6493 11.9733 14.2155 11.3964L14.2448 11.3663L14.2765 11.3403L14.4303 11.2223Z" fill="#299D55"/>
            </svg>
        )
    }
};

export default TaskIcon;
