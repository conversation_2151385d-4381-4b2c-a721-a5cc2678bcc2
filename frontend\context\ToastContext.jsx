"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';
import ToastContainer, { TOAST_TYPES } from '../components/ui/Toast';

const ToastContext = createContext();

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = useCallback((toast) => {
    const id = Date.now() + Math.random();
    const newToast = {
      id,
      type: TOAST_TYPES.INFO,
      duration: 5000,
      ...toast
    };

    setToasts(prev => [...prev, newToast]);
    return id;
  }, []);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const success = useCallback((message, title = null, options = {}) => {
    return addToast({
      type: TOAST_TYPES.SUCCESS,
      title,
      message,
      ...options
    });
  }, [addToast]);

  const error = useCallback((message, title = null, options = {}) => {
    return addToast({
      type: TOAST_TYPES.ERROR,
      title,
      message,
      duration: 7000, // Error messages stay longer
      ...options
    });
  }, [addToast]);

  const warning = useCallback((message, title = null, options = {}) => {
    return addToast({
      type: TOAST_TYPES.WARNING,
      title,
      message,
      ...options
    });
  }, [addToast]);

  const info = useCallback((message, title = null, options = {}) => {
    return addToast({
      type: TOAST_TYPES.INFO,
      title,
      message,
      ...options
    });
  }, [addToast]);

  const value = {
    toasts,
    addToast,
    removeToast,
    clearAllToasts,
    success,
    error,
    warning,
    info
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </ToastContext.Provider>
  );
};

export default ToastProvider;
