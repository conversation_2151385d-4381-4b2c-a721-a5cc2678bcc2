"use client";

import React, {useContext, useState, useEffect} from "react";
import Image from "next/image";
import {UserContext} from "../../context/UserProvider";
import AvatarDropdown from "./AvatarDropdown";
import ProfileModal from "./ProfileModal";
import PasswordChangeModal from "./PasswordChangeModal";
import PurchaseHistoryModal from "./PurchaseHistoryModal";
import {useRouter} from "next/navigation";
import InvoiceDetailsModal from "@/components/dashboard/InvoiceDetailsModal";
import {useScreenSize} from "@/hooks/useScreenSize";
import clsx from "clsx";

/**
 * Component Avatar hiển thị ảnh đại diện của người dùng
 * @param {Object} props
 * @param {string} props.alt - Alt text cho ảnh
 * @param {Function} props.onClick - Hàm x<PERSON> lý khi người dùng click vào avatar
 * @param {string} props.size - Kích thước avatar: "sm" | "md" | "lg"
 * @returns {JSX.Element}
 */
const Avatar = ({alt = "Avatar người dùng", onClick, size = "md"}) => {
    // State để quản lý trạng thái hiển thị dropdown
    const [showDropdown, setShowDropdown] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [isHoverAvatar, setIsHoverAvatar] = useState(false);
    const [showProfileModal, setShowProfileModal] = useState(false);
    const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
    const [showPurchaseHistoryModal, setShowPurchaseHistoryModal] =useState(false);
    const screenSize = useScreenSize();
    const router = useRouter();

    // Lấy thông tin người dùng từ UserContext
    const {user} = useContext(UserContext);

    // Lấy avatar URL từ thông tin người dùng
    const avatarSrc = user && user.image && user.image[0].url ?  process.env.NEXT_PUBLIC_STRAPI_URL +user.image[0].url : '';

    // Kiểm tra device là mobile/tablet hay desktop
    useEffect(() => {
        if (typeof window !== "undefined") {
            const checkIfMobile = () => {
                setIsMobile(window.innerWidth < 960); // 960px là breakpoint cho tablet theo Tailwind
            };

            // Kiểm tra lúc mount component
            checkIfMobile();

            // Kiểm tra khi resize window
            window.addEventListener("resize", checkIfMobile);

            // Cleanup
            return () => window.removeEventListener("resize", checkIfMobile);
        }
    }, []);

    // Kích thước dựa theo size
    const sizeClasses = {
        sm: "w-8 h-8",
        md: "w-10 h-10",
        lg: "w-16 h-16",
    };

    const sizeClass = sizeClasses[size] || sizeClasses.md;

    // Avatar mặc định khi không có src
    const DefaultAvatar = () => (
        <div
            className={`flex items-center justify-center ${sizeClass} `}
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
            >
                <path
                    d="M16.6654 17.5C16.6654 16.337 16.6654 15.7555 16.5218 15.2824C16.1987 14.217 15.365 13.3834 14.2997 13.0602C13.8265 12.9167 13.245 12.9167 12.082 12.9167H7.91537C6.7524 12.9167 6.17091 12.9167 5.69775 13.0602C4.63241 13.3834 3.79873 14.217 3.47556 15.2824C3.33203 15.7555 3.33203 16.337 3.33203 17.5M13.7487 6.25C13.7487 8.32107 12.0698 10 9.9987 10C7.92763 10 6.2487 8.32107 6.2487 6.25C6.2487 4.17893 7.92763 2.5 9.9987 2.5C12.0698 2.5 13.7487 4.17893 13.7487 6.25Z"
                    stroke="#717680"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </svg>
        </div>
    );

    // Xử lý click vào avatar
    const handleClick = () => {
        if (isMobile) {
            // Trên mobile/tablet: chuyển hướng đến trang tài khoản

            router.push("/tai-khoan");
        } else {
            // Trên desktop: hiển thị dropdown hoặc gọi onClick nếu được cung cấp
            setShowDropdown(!showDropdown);
            if (onClick) {
                onClick();
            }
        }
    };

    // Xử lý đóng dropdown
    const handleCloseDropdown = () => {
        setShowDropdown(false);
    };

    // Xử lý mở modal hồ sơ
    const handleOpenProfileModal = () => {
        setShowProfileModal(true);
    };

    // Xử lý đóng modal hồ sơ
    const handleCloseProfileModal = () => {
        setShowProfileModal(false);
    };

    // Xử lý mở modal đổi mật khẩu
    const handleOpenPasswordModal = () => {
        setShowChangePasswordModal(true);
    };

    // Xử lý đóng modal đổi mật khẩu
    const handleClosePasswordModal = () => {
        setShowChangePasswordModal(false);
    };

    // Xử lý mở modal lịch sử mua hàng
    const handleOpenPurchaseHistoryModal = () => {
        setShowPurchaseHistoryModal(true);
    };

    // Xử lý đóng modal lịch sử mua hàng
    const handleClosePurchaseHistoryModal = () => {
        setShowPurchaseHistoryModal(false);
    };
    const mouseEnter = () => {
        setIsHoverAvatar(true);
    }
    const mouseLeave = () => {
        setIsHoverAvatar(false);
    }

    // Tên hiển thị của người dùng (nếu có)
    const userName = user
        ? user.fullname || user.email || "Người dùng"
        : "Người dùng";

    return (
        // hover:bg-[#FAFAFA] hover:text-[#198C43]
        <>
            <div
                className="flex items-center gap-md cursor-pointer group w-full px-lg py-md"
                onClick={handleClick}
                onMouseEnter={mouseEnter}
                onMouseLeave={mouseLeave}
            >
                <button
                    className="rounded-full overflow-hidden focus:outline-none "
                    aria-label={`Avatar của ${userName}`}
                    title={userName}
                >
                    {avatarSrc ? (
                        <div className={clsx(
                            "relative group-hover:bg-[#299D55]",
                            sizeClass
                        )}>
                            <Image
                                src={avatarSrc}
                                alt={alt || `Avatar của ${userName}`}
                                fill
                                className="object-cover rounded-full  group-hover:bg-[#299D55]"
                            />
                        </div>
                    ) : (
                        isHoverAvatar ?
                            <div className="flex items-center justify-center w-full">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <g clipPath="url(#clip0_4709_9017)">
                                        <path fillRule="evenodd" clipRule="evenodd" d="M9.99992 0.833313C15.0625 0.833313 19.1666 4.93737 19.1666 9.99998C19.1666 10.3051 19.151 10.6067 19.1218 10.9041C19.1201 10.922 19.118 10.9399 19.1161 10.9578C19.1022 11.0916 19.0853 11.2245 19.0657 11.3566C19.0622 11.3803 19.058 11.4038 19.0543 11.4274C19.0339 11.5577 19.0109 11.6871 18.9851 11.8156C18.9812 11.8351 18.9778 11.8547 18.9737 11.8742C18.9468 12.0036 18.9165 12.1318 18.8842 12.2591C18.878 12.2835 18.8719 12.308 18.8655 12.3323C18.834 12.4524 18.8008 12.5716 18.7646 12.6896C18.7536 12.7253 18.7418 12.7607 18.7304 12.7962C18.6954 12.9054 18.6603 13.0143 18.6213 13.1217C18.612 13.1474 18.6016 13.1726 18.592 13.1982C18.5629 13.2765 18.5313 13.3537 18.5001 13.431C18.4645 13.5191 18.4268 13.6062 18.3886 13.693C18.3626 13.7519 18.336 13.8105 18.3088 13.8688C18.2724 13.947 18.2351 14.0246 18.1965 14.1015C18.1657 14.163 18.1343 14.224 18.1021 14.2846C18.058 14.3679 18.0128 14.4503 17.9662 14.532C17.9404 14.5773 17.9146 14.6224 17.8881 14.6671C17.8282 14.7681 17.7661 14.8674 17.7026 14.9658C17.686 14.9914 17.6705 15.0177 17.6537 15.0431C17.582 15.1517 17.5069 15.2577 17.4307 15.3629C17.4153 15.3842 17.4007 15.4061 17.3852 15.4272C15.7164 17.6943 13.0309 19.1666 9.99992 19.1666C7.13241 19.1666 4.57305 17.8495 2.89217 15.7877C2.86457 15.7539 2.83873 15.7186 2.8116 15.6844C2.75064 15.6074 2.68945 15.5306 2.63094 15.4516C2.58797 15.3937 2.54724 15.3341 2.50562 15.275C2.45732 15.2066 2.40884 15.1382 2.36239 15.0683C2.32848 15.0173 2.29601 14.9654 2.2631 14.9137C2.21361 14.836 2.16462 14.7579 2.11743 14.6785C2.08464 14.6234 2.05307 14.5676 2.0214 14.5117C1.9788 14.4365 1.93659 14.3611 1.89608 14.2846C1.86303 14.2223 1.83168 14.159 1.80005 14.0958C1.76424 14.0243 1.72823 13.9528 1.69425 13.8802C1.66324 13.8139 1.63338 13.7471 1.60392 13.68C1.56741 13.5968 1.53222 13.513 1.49813 13.4285C1.46825 13.3545 1.43822 13.2805 1.41024 13.2055C1.39979 13.1776 1.38868 13.1498 1.3785 13.1217C1.33695 13.007 1.29836 12.891 1.26131 12.7742C1.25271 12.7471 1.24363 12.7201 1.23527 12.6928C1.19741 12.5695 1.16302 12.4448 1.13029 12.3193C1.12604 12.303 1.12143 12.2868 1.11727 12.2705C1.09149 12.1693 1.06641 12.0678 1.04403 11.9653L1.01961 11.8473C1.01734 11.8362 1.01533 11.8251 1.0131 11.8139C0.95685 11.5338 0.912961 11.249 0.882894 10.9603C0.880835 10.9405 0.87913 10.9207 0.877197 10.9009C0.84828 10.6045 0.833252 10.304 0.833252 9.99998C0.833252 4.93737 4.93731 0.833313 9.99992 0.833313ZM7.49992 13.3333C6.20388 13.3334 5.04677 13.9252 4.28296 14.8519C5.65867 16.4713 7.70859 17.5 9.99992 17.5C12.2911 17.5 14.3404 16.471 15.7161 14.8519C14.9523 13.9254 13.7958 13.3333 12.4999 13.3333H7.49992ZM9.99992 3.74998C7.6989 3.75017 5.83325 5.61558 5.83325 7.91665C5.83325 10.2177 7.6989 12.0831 9.99992 12.0833C12.3011 12.0833 14.1666 10.2178 14.1666 7.91665C14.1666 5.61549 12.3011 3.75002 9.99992 3.74998Z" fill="#299D55"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_4709_9017">
                                            <rect width="20" height="20" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                            </div>
                        :
                            <div className="flex items-center justify-center w-full">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <g clipPath="url(#clip0_4709_13228)">
                                        <path d="M4.43027 16.1986C4.9372 15.0043 6.12079 14.1666 7.50002 14.1666H12.5C13.8793 14.1666 15.0628 15.0043 15.5698 16.1986M13.3334 7.91663C13.3334 9.75758 11.841 11.25 10 11.25C8.15907 11.25 6.66669 9.75758 6.66669 7.91663C6.66669 6.07568 8.15907 4.58329 10 4.58329C11.841 4.58329 13.3334 6.07568 13.3334 7.91663ZM18.3334 9.99996C18.3334 14.6023 14.6024 18.3333 10 18.3333C5.39765 18.3333 1.66669 14.6023 1.66669 9.99996C1.66669 5.39759 5.39765 1.66663 10 1.66663C14.6024 1.66663 18.3334 5.39759 18.3334 9.99996Z"
                                              stroke="#717680" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_4709_13228">
                                            <rect width="20" height="20" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                            </div>
                    )}
                </button>
                {
                   !screenSize?.gt1280 ? "" : <p className="text-md leading-md font-semibold text-secondary-700 group-hover:text-brand-secondary">Tài khoản</p>
                }

            </div>


            {/* Chỉ hiển thị dropdown trên desktop */}
            {!isMobile && (
                <AvatarDropdown
                    isOpen={showDropdown}
                    onClose={handleCloseDropdown}
                    onOpenProfile={handleOpenProfileModal}
                    onOpenPasswordChange={handleOpenPasswordModal}
                    onOpenPurchaseHistory={handleOpenPurchaseHistoryModal}
                />
            )}

            {/* Di chuyển modals lên đây */}
            <ProfileModal
                isOpen={showProfileModal}
                onClose={handleCloseProfileModal}
            />
            <PasswordChangeModal
                isOpen={showChangePasswordModal}
                onClose={handleClosePasswordModal}
            />
            {/*<PurchaseHistoryModal*/}
            {/*  isOpen={showPurchaseHistoryModal}*/}
            {/*  onClose={handleClosePurchaseHistoryModal}*/}
            {/*/>*/}
            <InvoiceDetailsModal isOpen={showPurchaseHistoryModal}
                                 onClose={handleClosePurchaseHistoryModal}></InvoiceDetailsModal>
        </>
    );
};

export default Avatar;
