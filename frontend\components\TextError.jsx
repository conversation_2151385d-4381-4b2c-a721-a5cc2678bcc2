"use client";
import React, { useState, useEffect, useRef } from "react";
import clsx from "clsx";

const TextError = (
    {
        error = null,
        helperText = null
    }
) => {

  if (error ||  helperText) {
    return (
        <span
            className={clsx("text-sm", {
                "text-[#535862]": !error,
                "text-[#F04438]": error,
            })}
        >
          {error || helperText}
        </span>
    );
  }
};

export default TextError;
