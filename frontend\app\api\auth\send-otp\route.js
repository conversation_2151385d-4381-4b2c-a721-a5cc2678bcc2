import { NextResponse } from 'next/server'
import strapi from '../../strapi'

export async function POST(request) {
    try {
        // Lấy email từ body
        const { email } = await request.json()

        if (!email) {
            return NextResponse.json(
                { success: false, error: 'Email là bắt buộc' },
                { status: 400 }
            )
        }

        // Gọi API gửi OTP từ Strapi
        const response = await strapi.sendOTP.send(email)

        // Nếu gửi OTP thành công
        if (response.success) {
            return NextResponse.json({
                success: true,
                message: 'G<PERSON>i mã OTP thành công'
            })
        }

        // Tr<PERSON> về lỗi nếu không thành công
        return NextResponse.json(
            { success: false, error: 'Không thể gửi mã OTP' },
            { status: 400 }
        )
    } catch (error) {
        console.error('Lỗi gửi OTP:', error)
        return NextResponse.json(
            { success: false, error: 'G<PERSON><PERSON> mã OTP thất bại. Vui lòng thử lại sau.' },
            { status: 500 }
        )
    }
} 