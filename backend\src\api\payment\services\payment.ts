/**
 * payment service
 */

import axios from "axios";
import crypto from "crypto";

const generateHmacSha256 = (data: string, secretKey: string) => {
  return crypto.createHmac("sha256", secretKey).update(data).digest("hex");
};

export default {
  async createPaymentLink(data) {
    try {
      console.log("Creating PayOS payment with:", data);

      const { orderId, amount } = data;

      if (!orderId || !amount) {
        throw new Error("Missing required fields: orderId or amount");
      }

      // 1. L<PERSON>y số lượng order và log
      const ordersCount = await strapi.db.query("api::order.order").count();
      console.log("Current orders count:", ordersCount);

      // 2. Tạo payos_order_code và log
      const orderNumber = ordersCount.toString().padStart(3, "0");
      const payosOrderCode = `ABA${orderNumber}`;
      console.log("Generated payos_order_code:", payosOrderCode);
      const orderCode = Date.now();

      // 3. Update order và log kết quả
      try {
        const updateResult = await strapi.db.query("api::order.order").update({
          where: { id: orderId },
          data: {
            payos_order_code: payosOrderCode,
            orderCode: orderCode,
            published_at: new Date(),
            updated_at: new Date(),
          },
        });
        console.log("Order update result:", updateResult);
      } catch (updateError) {
        console.error("Error updating order:", {
          error: updateError,
          orderId,
          payosOrderCode,
        });
        throw updateError;
      }

      // 4. Kiểm tra lại order sau khi update
      const updatedOrder = await strapi.db.query("api::order.order").findOne({
        where: { id: orderId },
      });
      console.log("Updated order:", updatedOrder);

      // 5. Tạo description với mã order
      const description = `ABA${orderNumber}`;

      // 6. Tạo payload cho PayOS
      const paymentData = {
        orderCode: orderCode,
        amount: Number(amount),
        description: description,
        cancelUrl: `${process.env.PAYOS_REDIRECT_URL}/thanh-toan`,
        returnUrl: `${process.env.PAYOS_REDIRECT_URL}/hoa-don`,
        items: [
          {
            name: `Thanh toan don hang ${payosOrderCode}`,
            quantity: 1,
            price: Number(amount),
          },
        ],
      };

      // 7. Tạo signature
      const dataStr = `amount=${paymentData.amount}&cancelUrl=${paymentData.cancelUrl}&description=${paymentData.description}&orderCode=${paymentData.orderCode}&returnUrl=${paymentData.returnUrl}`;
      const signature = generateHmacSha256(
        dataStr,
        process.env.PAYOS_CHECKSUM_KEY
      );

      paymentData["signature"] = signature;

      console.log("PayOS request payload:", paymentData);

      const response = await axios.post(
        "https://api-merchant.payos.vn/v2/payment-requests",
        paymentData,
        {
          headers: {
            "x-client-id": process.env.PAYOS_CLIENT_ID,
            "x-api-key": process.env.PAYOS_API_KEY,
            "Content-Type": "application/json",
          },
        }
      );

      console.log("PayOS response:", response.data);

      if (response.data.code !== "00") {
        throw new Error(`PayOS request failed: ${response.data.desc}`);
      }

      if (!response.data.data?.checkoutUrl) {
        throw new Error("Missing checkout URL in PayOS response");
      }

      return {
        checkoutUrl: response.data.data.checkoutUrl,
        orderCode: payosOrderCode,
      };
    } catch (error) {
      console.error("PayOS API error:", {
        message: error.message,
        response: error.response?.data,
        stack: error.stack,
      });
      throw error;
    }
  },

  async verifyPayment(payosOrderId) {
    try {
      // 1. Gọi API verify của PayOS
      const verifyResponse = await axios.get(
        `https://api-merchant.payos.vn/v2/payment-requests/${payosOrderId}`,
        {
          headers: {
            "x-client-id": process.env.PAYOS_CLIENT_ID,
            "x-api-key": process.env.PAYOS_API_KEY,
            "Content-Type": "application/json",
          },
        }
      );

      console.log("PayOS verify response:", verifyResponse.data);

      // 2. Kiểm tra response code
      if (verifyResponse.data.code !== "00") {
        throw new Error(
          `PayOS verification failed: ${verifyResponse.data.desc}`
        );
      }

      const paymentStatus = verifyResponse.data.data.status;
      console.log("Payment status:", paymentStatus);

      // 3. Tìm order theo payos_order_code
      const order = await strapi.db.query("api::order.order").findOne({
        where: { payos_order_code: payosOrderId },
      });

      if (!order) {
        throw new Error("Order not found");
      }

      // 4. Xử lý khi thanh toán thành công
      if (paymentStatus === "PAID") {
        // Tìm activation code chưa sử dụng
        const unusedCode = await strapi.db
          .query("api::activation-code.activation-code")
          .findOne({
            where: { activation_status: false },
            orderBy: { id: "asc" },
          });

        if (!unusedCode) {
          throw new Error("No available activation codes");
        }

        // Cập nhật order và liên kết activation code
        await strapi.db.query("api::order.order").update({
          where: { id: order.id },
          data: {
            payment_status: "completed",
            activation_codes: { connect: [unusedCode.id] },
          },
        });

        // Cập nhật trạng thái activation code
        await strapi.db.query("api::activation-code.activation-code").update({
          where: { id: unusedCode.id },
          data: {
            activation_status: true,
            published_at: new Date(),
            updated_at: new Date(),
          },
        });

        return {
          verified: true,
          orderId: order.id,
          activationCode: unusedCode.code,
          paymentStatus: paymentStatus,
        };
      }

      // 5. Trả về kết quả nếu chưa thanh toán
      return {
        verified: false,
        paymentStatus: paymentStatus,
      };
    } catch (error) {
      console.error("Verify payment error:", {
        message: error.message,
        response: error.response?.data,
        stack: error.stack,
      });
      throw error;
    }
  },

  async handleWebhook(body, headers) {
    try {
      // Verify webhook signature
      const signature = headers["x-signature"];
      const expectedSignature = generateHmacSha256(
        JSON.stringify(body),
        process.env.PAYOS_CHECKSUM_KEY
      );

      if (signature !== expectedSignature) {
        throw new Error("Invalid signature");
      }

      const { orderCode, status } = body;
      console.log("Webhook received:", { orderCode, status });

      // Tìm order
      const order = await strapi.db.query("api::order.order").findOne({
        where: { payos_order_code: orderCode },
        populate: ["users_permissions_user", "course", "activation_codes"],
      });

      if (!order) {
        throw new Error("Order not found");
      }

      // Xử lý khi thanh toán thành công
      if (status === "PAID" && order.payment_status !== "completed") {
        // Sử dụng lại hàm completePayment để xử lý
        await this.completePayment(order.orderCode);
      }

      return { success: true };
    } catch (error) {
      console.error("Webhook processing error:", error);
      throw error;
    }
  },
  async completePayment(orderCode) {
    try {
      console.log("=== Start Complete Payment ===");
      console.log("Input orderCode:", orderCode);

      // Tìm order
      const order = await strapi.db.query("api::order.order").findOne({
        where: { orderCode: orderCode },
        populate: ["users_permissions_user", "course", "activation_codes"],
      });

      console.log(
        "Order details before update:",
        JSON.stringify(order, null, 2)
      );

      if (!order) {
        throw new Error(`Order not found for orderCode: ${orderCode}`);
      }

      // Nếu đơn hàng chưa hoàn thành, cập nhật trạng thái
      if (order.payment_status !== "completed") {
        // Tìm activation code chưa sử dụng
        const unusedCode = await strapi.db
          .query("api::activation-code.activation-code")
          .findOne({
            where: { activation_status: false },
          });

        if (!unusedCode) {
          throw new Error("No available activation codes");
        }

        // Cập nhật order
        await strapi.db.query("api::order.order").update({
          where: { id: order.id },
          data: {
            payment_status: "completed",
            activation_codes: { connect: [unusedCode.id] },
          },
        });

        // Cập nhật activation code
        await strapi.db.query("api::activation-code.activation-code").update({
          where: { id: unusedCode.id },
          data: {
            activation_status: true,
            published_at: new Date(),
            updated_at: new Date(),
          },
        });

        // Lấy lại thông tin order sau khi cập nhật
        const updatedOrder = await strapi.db.query("api::order.order").findOne({
          where: { id: order.id },
          populate: ["users_permissions_user", "course", "activation_codes"],
        });

        console.log(
          "Updated Order details:",
          JSON.stringify(updatedOrder, null, 2)
        );

        // Gửi email nếu chưa gửi
        if (!updatedOrder.email_sent) {
          await sendPaymentConfirmationEmail(updatedOrder);

          // Cập nhật trạng thái đã gửi email
          await strapi.db.query("api::order.order").update({
            where: { id: updatedOrder.id },
            data: {
              email_sent: true,
            },
          });
        }
      }

      return {
        success: true,
        orderId: order.id,
        activationCode: order.activation_codes?.[0]?.code,
      };
    } catch (error) {
      console.error("Complete payment error:", error);
      throw error;
    }
  },
};

// Hàm gửi email từ backend
async function sendPaymentConfirmationEmail(order) {
  try {
    // Kiểm tra xem có mã kích hoạt không
    if (!order.activation_codes || order.activation_codes.length === 0) {
      throw new Error("No activation code available");
    }

    const emailDetails = {
      email: order.users_permissions_user.email,
      name: order.users_permissions_user.fullname,
      phone: order.users_permissions_user.phone,
      orderId: order.payos_order_code,
      paymentTime: order.createdAt || new Date().toISOString(),
      paymentMethod: "QR",
      totalAmount: (order.total_amount || 0).toString(),
      discount: (order.discount || 0).toString(),
      courseName: order.course?.title,
      courseFee: (order.total_amount || 0).toString(),
      address: order.delivery_address,
      code: order.activation_codes[0].code, // Đảm bảo activation_codes có ít nhất 1 phần tử
    };

    // Gửi email
    return await strapi
      .service("api::send-otp.send-otp")
      .sendPaymentConfirmationEmail(
        emailDetails.email,
        emailDetails.name,
        emailDetails.phone,
        emailDetails.orderId,
        emailDetails.paymentTime,
        emailDetails.paymentMethod,
        emailDetails.totalAmount,
        emailDetails.discount,
        emailDetails.courseName,
        emailDetails.courseFee,
        emailDetails.address,
        emailDetails.code
      );
  } catch (error) {
    console.error("Failed to send payment confirmation email:", error);
    throw error;
  }
}
