import React, { useEffect, useRef, useContext, useMemo, useState } from "react";
import { UserContext } from "../../context/UserProvider";
import BadgeCourse from "./BadgeCourse";
import StatusBadge from "./StatusBadge";
import Button from "../Button";
import InvoiceDetailsModal from "./InvoiceDetailsModal";

/**
 * @typedef {Object} Purchase
 * @property {string} id
 * @property {string} courseName
 * @property {string} course_tier_type
 * @property {string} status
 * @property {string} statusColor
 * @property {string} paymentDate
 * @property {string} price
 * @property {function} onViewInvoice
 * @property {Object} course_tier - Thông tin về khóa học
 * @property {string} course_tier.startDate - Ngày bắt đầu
 * @property {string} course_tier.endDate - Ngày kết thúc
 */

/**
 * @param {{ isOpen: boolean, onClose: function, extraPurchaseList?: Purchase[] }} props
 */
const PurchaseHistoryModal = ({ isOpen, onClose, extraPurchaseList = [] }) => {
  const modalRef = useRef(null);
  const { completedOrderInfo } = useContext(UserContext);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Tạo danh sách mua hàng từ completedOrderInfo và extraPurchaseList
  const purchaseList = useMemo(() => {
    const list = [...extraPurchaseList];

    if (completedOrderInfo) {
      // Lấy thông tin từ completedOrderInfo
      const orderId = completedOrderInfo.id;
      const courseTitle = completedOrderInfo.course?.title;
      const startDate = completedOrderInfo.course_tier.startDate;
      const endDate = completedOrderInfo.course_tier.endDate;
      const course_tier_type = completedOrderInfo.course_tier.tier_type;
      const orderDate = completedOrderInfo.order_date;
      const totalAmount = completedOrderInfo.total_amount;

      // Format ngày thanh toán
      const formattedDate = new Date(orderDate).toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });

      // Format số tiền
      const formattedAmount = totalAmount.toLocaleString("vi-VN") + "đ";

      // Tạo item mới nếu chưa có trong danh sách
      if (!list.some((item) => item.id === orderId)) {
        list.unshift({
          id: orderId,
          courseName: courseTitle,
          course_tier_type: course_tier_type,
          course_tier: {
            startDate,
            endDate,
          },
          paymentDate: formattedDate,
          price: formattedAmount,
          orderInfo: completedOrderInfo, // Lưu thông tin đơn hàng đầy đủ
          onViewInvoice: () => {
            // Hiển thị modal chi tiết hóa đơn
            setSelectedOrder(completedOrderInfo);
            setShowInvoiceModal(true);
          },
        });
      }
    }

    return list;
  }, [completedOrderInfo, extraPurchaseList]);

  // Focus trap & close on ESC
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (e) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  const handleCloseInvoiceModal = () => {
    setShowInvoiceModal(false);
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 z-50 flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
        tabIndex={-1}
        aria-modal="true"
        role="dialog"
        onClick={onClose}
      >
        <div
          className="bg-[#FFFFFF] rounded-2xl shadow-xl w-full max-w-[466px] p-6 relative"
          ref={modalRef}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close button */}
          <button
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none"
            aria-label="Đóng"
            tabIndex={0}
            onClick={onClose}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") onClose();
            }}
          >
            <svg width="24" height="24" fill="none">
              <path
                d="M18 6L6 18M6 6l12 12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </button>
          {/* Title */}
          <h2 className="text-xl font-semibold text-center mb-5">
            Lịch sử mua
          </h2>
          {/* Purchase List */}
          <div className="space-y-4">
            {purchaseList.length > 0 ? (
              purchaseList.map((purchase) => (
                <div
                  key={purchase.id}
                  className="border border-[#E9EAEB] rounded-xl p-6 flex flex-col gap-2"
                >
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-[#222]">
                      {purchase.courseName}
                    </span>
                    <BadgeCourse
                      plan={purchase.course_tier_type}
                      type="Default"
                      size="sm"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs leading-[18px] text-[#535862] font-normal w-[127px]">
                      Trạng thái
                    </span>
                    <StatusBadge
                      startDate={purchase.course_tier?.startDate}
                      endDate={purchase.course_tier?.endDate}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs leading-[18px] text-[#535862] font-normal w-[127px]">
                      Ngày mua
                    </span>
                    <span className="text-xs leading-[18px] text-[#535862] font-medium">
                      {purchase.paymentDate}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs leading-[18px] text-[#535862] font-normal w-[127px]">
                      Giá
                    </span>
                    <span className="text-xs leading-[18px] text-[#535862] font-medium">
                      {purchase.price}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="secondaryGray"
                      className="w-full mt-6"
                      tabIndex={0}
                      aria-label="Xem hoá đơn"
                      onClick={purchase.onViewInvoice}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ")
                          purchase.onViewInvoice();
                      }}
                    >
                      Xem hoá đơn
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                Bạn chưa có lịch sử mua hàng nào
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Invoice Details Modal */}
      <InvoiceDetailsModal
        isOpen={showInvoiceModal}
        onClose={handleCloseInvoiceModal}
        orderInfo={selectedOrder}
      />
    </>
  );
};

export default PurchaseHistoryModal;
