"use client";
import React, { useState, useEffect, Suspense, useContext } from "react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import Button from "../../components/Button";
import strapi from "../api/strapi";
import { toast } from "react-hot-toast";
import axios from "axios";
import { UserContext } from "../../context/UserProvider";
import logo from "../../public/images/Logo.png";

function InvoiceContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [verifyStatus, setVerifyStatus] = useState("pending");
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [emailSent, setEmailSent] = useState(false);
  const { user, checkOrderStatus } = useContext(UserContext);

  useEffect(() => {
    const orderCode = searchParams.get("orderCode");
    const isVerified = searchParams.get("verified");

    if (!orderCode) return;

    // Kiểm tra nếu đã xác minh thanh toán cho orderCode này trước đó
    const verifiedOrders = JSON.parse(
      localStorage.getItem("verifiedOrders") || "{}"
    );

    // Nếu chưa có trong localStorage hoặc isVerified chưa được set
    if (!verifiedOrders[orderCode] && !isVerified) {
      verifyPayment(orderCode).then((success) => {
        if (success) {
          // Đánh dấu là đã xác minh trong localStorage
          verifiedOrders[orderCode] = true;
          localStorage.setItem(
            "verifiedOrders",
            JSON.stringify(verifiedOrders)
          );

          // Thêm tham số verified vào URL
          const params = new URLSearchParams(window.location.search);
          params.set("verified", "true");
          window.history.replaceState(
            {},
            "",
            `${window.location.pathname}?${params.toString()}`
          );

          // Sau khi verify xong, lấy thông tin đơn hàng
          getOrderDetails(orderCode);
        }
      });
    } else {
      getOrderDetails(orderCode);
    }
  }, [searchParams]);

  const verifyPayment = async (orderCode) => {
    try {
      if (!orderCode) return false;

      // Đảm bảo sử dụng await khi gọi API
      const response = await strapi.payment.completePayment(orderCode);

      if (response?.data) {
        setVerifyStatus("success");
        await checkOrderStatus();
        return true;
      } else {
        setVerifyStatus("failed");
        return false;
      }
    } catch (error) {
      setVerifyStatus("failed");
      return false;
    }
  };

  const getOrderDetails = async (orderCode) => {
    try {
      setLoading(true);

      const data = await strapi.orders.getOrderByOrderCode(orderCode);

      if (data?.data) {
        const orderData = data.data;
        setOrder({
          id: orderData.id,
          ...orderData,
          course: orderData.course,
          user: orderData.users_permissions_user,
          activation_code: orderData.activation_codes?.[0],
        });

        if (orderData.payment_status === "completed") {
          await checkOrderStatus();
        }
      } else {
        setError("Không tìm thấy thông tin đơn hàng");
      }
    } catch (error) {
      setError("Có lỗi xảy ra khi lấy thông tin đơn hàng");
    } finally {
      setLoading(false);
    }
  };

  const handleDiscordRedirect = () => {
    window.open("https://discord.com/invite/EZcY6kd7eb", "_blank");
  };

  const formatPrice = (price) => {
    if (!price) return "0";
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const copyToClipboard = (text) => {
    if (text) {
      navigator.clipboard.writeText(text).then(
        () => {
          toast.success("Đã sao chép mã kích hoạt");
        },
        () => {
          toast.error("Không thể sao chép, vui lòng thử lại");
        }
      );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <p className="text-lg">Đang tải thông tin...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center p-4">
        <h2 className="text-xl font-semibold text-red-600 mb-4">{error}</h2>
        <Button variant="primary" onClick={() => router.push("/thanh-toan")}>
          Quay lại trang thanh toán
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#FFFFFF] py-12 max-md:py-8 px-4">
      <div className="max-w-[590px] mx-auto px-6 border border-[#E9EAEB] rounded-2xl py-12">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <Image
            src={logo}
            alt="Logo"
            width={130}
            height={48}
            className="object-contain"
            onClick={() => router.push("/")}
          />
        </div>

        {/* Main Content */}
        <div className="max-w-[590px] mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl max-sm:text-2xl font-semibold text-[#181D27] mb-3">
              Đăng ký khoá học thành công
            </h1>
            <p className="text-base font-normal text-[#535862]">
              Thầy Ba xin chân thành cảm ơn bạn đã đăng ký tham gia khoá học.
            </p>
          </div>

          {/* Activation Code Section */}
          {order?.activation_code && (
            <div className="mb-12">
              <p className="text-lg font-semibold text-[#535862] mb-2 text-center">
                MÃ KÍCH HOẠT VÀO LỚP
              </p>

              <div className="w-full max-w-[320px] mx-auto mb-[20px]">
                <div className="flex items-center justify-center w-full gap-[6px] px-[20px] py-[7px] rounded-lg border border-[#D5D7DA]">
                  <div className="text-2xl font-semibold text-[#198C43]">
                    {order.activation_code.code}
                  </div>
                  <button
                    className="p-2 rounded-lg hover:bg-[#F3F4F6]"
                    onClick={() => copyToClipboard(order.activation_code.code)}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M16.6667 7.5H8.33333C7.8731 7.5 7.5 7.8731 7.5 8.33333V16.6667C7.5 17.1269 7.8731 17.5 8.33333 17.5H16.6667C17.1269 17.5 17.5 17.1269 17.5 16.6667V8.33333C17.5 7.8731 17.1269 7.5 16.6667 7.5Z"
                        stroke="#414651"
                        strokeWidth="1.66667"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M4.16675 12.5H3.33341C2.89139 12.5 2.46746 12.3244 2.15491 12.0118C1.84235 11.6993 1.66675 11.2754 1.66675 10.8333V3.33333C1.66675 2.89131 1.84235 2.46738 2.15491 2.15483C2.46746 1.84228 2.89139 1.66667 3.33341 1.66667H10.8334C11.2754 1.66667 11.6994 1.84228 12.0119 2.15483C12.3245 2.46738 12.5001 2.89131 12.5001 3.33333V4.16667"
                        stroke="#414651"
                        strokeWidth="1.66667"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  className="w-full max-w-[320px] flex justify-center items-center gap-3 bg-[#5865F2] hover:bg-[#4852BC] text-[#FFFFFF] text-base font-semibold px-4 py-[10px] border-2 rounded-lg"
                  onClick={handleDiscordRedirect}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="25"
                    height="19"
                    viewBox="0 0 25 19"
                    fill="none"
                  >
                    <path
                      d="M20.6326 1.50265C19.1168 0.807139 17.4914 0.294717 15.7918 0.00123113C15.7609 -0.00443304 15.73 0.00972204 15.714 0.0380329C15.505 0.409842 15.2734 0.894898 15.1113 1.27615C13.2833 1.00249 11.4648 1.00249 9.6743 1.27615C9.51211 0.886424 9.27214 0.409842 9.06216 0.0380329C9.04621 0.0106666 9.01529 -0.00348846 8.98434 0.00123113C7.28576 0.293779 5.6603 0.806201 4.14356 1.50265C4.13043 1.50831 4.11918 1.51776 4.11171 1.53002C1.02855 6.13619 0.183944 10.6291 0.59828 15.0664C0.600155 15.0881 0.612341 15.1089 0.629215 15.1221C2.66339 16.6159 4.63385 17.5228 6.56771 18.1239C6.59866 18.1334 6.63145 18.1221 6.65114 18.0966C7.1086 17.4719 7.51638 16.8132 7.86601 16.1205C7.88665 16.0799 7.86695 16.0318 7.82478 16.0157C7.17797 15.7704 6.56208 15.4712 5.96963 15.1315C5.92277 15.1041 5.91902 15.0371 5.96213 15.005C6.0868 14.9116 6.21151 14.8144 6.33055 14.7163C6.35209 14.6983 6.3821 14.6946 6.40743 14.7059C10.2995 16.4829 14.5132 16.4829 18.3594 14.7059C18.3847 14.6936 18.4147 14.6974 18.4372 14.7153C18.5563 14.8135 18.6809 14.9116 18.8066 15.005C18.8497 15.0371 18.8468 15.1041 18.8 15.1315C18.2075 15.4778 17.5917 15.7704 16.9439 16.0148C16.9017 16.0308 16.883 16.0799 16.9036 16.1205C17.2607 16.8122 17.6685 17.4709 18.1175 18.0956C18.1363 18.1221 18.17 18.1334 18.201 18.1239C20.1442 17.5228 22.1147 16.6159 24.1488 15.1221C24.1667 15.1089 24.1779 15.089 24.1798 15.0673C24.6757 9.93739 23.3492 5.48128 20.6635 1.53095C20.657 1.51776 20.6458 1.50831 20.6326 1.50265ZM8.44724 12.3646C7.27544 12.3646 6.30992 11.2888 6.30992 9.9676C6.30992 8.64641 7.25672 7.57062 8.44724 7.57062C9.6471 7.57062 10.6033 8.65586 10.5845 9.9676C10.5845 11.2888 9.63772 12.3646 8.44724 12.3646ZM16.3496 12.3646C15.1778 12.3646 14.2123 11.2888 14.2123 9.9676C14.2123 8.64641 15.1591 7.57062 16.3496 7.57062C17.5495 7.57062 18.5056 8.65586 18.4869 9.9676C18.4869 11.2888 17.5495 12.3646 16.3496 12.3646Z"
                      fill="white"
                    />
                  </svg>
                  Tham gia Discord
                </button>
              </div>
              <p className="text-sm text-[#717680] text-center mt-3 underline">
                Hướng dẫn sử dụng mã kích hoạt
              </p>
            </div>
          )}

          {/* Order Information */}
          {order && (
            <div className="bg-white">
              <h2 className="text-lg font-semibold text-[#535862] mb-2">
                THÔNG TIN ĐƠN HÀNG
              </h2>
              <div className="sm:px-6">
                {/* Personal and Delivery Information */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 border-b border-[#E9EAEB] pb-4 mb-4">
                  <div className="max-sm:border-b max-sm:border-[#E9EAEB] max-sm:pb-4">
                    <h3 className="text-base font-semibold text-[#181D27] mb-[6px]">
                      Thông tin cá nhân
                    </h3>
                    <div>
                      <p className="text-base font-normal text-[#535862]">
                        {order.user?.fullname}
                      </p>
                      <p className="text-base font-normal text-[#535862]">
                        {order.user?.phone}
                      </p>
                      <p className="text-base font-normal text-[#535862]">
                        {order.user?.email}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-base font-semibold text-[#181D27] mb-[6px]">
                      Tài liệu giao đến
                    </h3>
                    <p className="text-base font-normal text-[#535862]">
                      {order.delivery_address}
                    </p>
                  </div>
                </div>

                {/* Order Details */}
                <div className="mb-4 pb-4 border-b border-[#E9EAEB]">
                  <h3 className="text-base font-semibold text-[#181D27] mb-[6px]">
                    Chi tiết đơn hàng
                  </h3>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-2">
                    <p className="text-base font-normal text-[#535862]">
                      Mã đơn hàng
                    </p>
                    <p className="text-base font-normal text-[#535862] sm:text-right">
                      #{order.payos_order_code}
                    </p>
                    <p className="text-base font-normal text-[#535862]">
                      Thời gian thanh toán
                    </p>
                    <p className="text-base font-normal text-[#535862] sm:text-right">
                      {formatDate(order.updatedAt || order.createdAt)}
                    </p>
                    <p className="text-base font-normal text-[#535862]">
                      Phương thức thanh toán
                    </p>
                    <p className="text-base font-normal text-[#535862] sm:text-right">
                      Quét mã QR
                    </p>
                  </div>
                </div>

                {/* Course Information */}
                <div>
                  <h3 className="text-base font-semibold text-[#181D27] mb-[6px]">
                    {order.course?.title}
                  </h3>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-base font-normal text-[#535862]">
                        Học phí
                      </span>
                      <span className="text-base font-normal text-[#535862]">
                        {formatPrice(order.total_amount) + "đ"}
                      </span>
                    </div>
                    <div className="flex justify-between mb-4">
                      <span className="text-base font-normal text-[#535862]">
                        Ưu đãi
                      </span>
                      <span className="text-base font-normal text-[#535862]">
                        {order.discount
                          ? formatPrice(order.discount) + "đ"
                          : "0đ"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between pt-4 border-t border-[#E9EAEB]">
                      <span className="text-base font-semibold text-[#181D27]">
                        Tổng thanh toán
                      </span>
                      <span className="text-2xl font-bold text-[#181D27]">
                        {formatPrice(order.total_amount) + "đ"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Bottom Buttons */}
          <div className="flex flex-col-reverse sm:flex-row gap-3 mt-8">
            <Button
              variant="primary"
              className="flex-1"
              onClick={() => router.push("/quan-ly")}
            >
              Quản lý khoá học
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

const InvoicePage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <InvoiceContent />
    </Suspense>
  );
};

export default InvoicePage;
