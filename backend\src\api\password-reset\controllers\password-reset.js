'use strict';

module.exports = {
  async sendOTP(ctx) {
    try {
      const { email } = ctx.request.body;

      if (!email) {
        return ctx.badRequest('Email là bắt buộc');
      }

      const user = await strapi.db.query('plugin::users-permissions.user').findOne({
        where: { email }
      });

      if (!user) {
        return ctx.badRequest('Email không tồn tại trong hệ thống');
      }

      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          resetPasswordToken: otp,
          resetPasswordExpiration: new Date(Date.now() + 15 * 60 * 1000),
        },
      });

      const emailTemplate = {
        subject: 'Đặt lại mật khẩu - Ông Bà Dạy Hoa',
        text: `<PERSON>ã OTP của bạn là: <%= otp %>. <PERSON><PERSON> này sẽ hết hạn sau 15 phút.`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #343a40; text-align: center;">Đặt lại mật khẩu</h2>
            <p style="color: #495057;">Xin chào,</p>
            <p style="color: #495057;">Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng sử dụng mã OTP dưới đây:</p>
            <div style="font-size: 24px; font-weight: bold; color: #007bff; text-align: center; margin: 20px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
              <%= otp %>
            </div>
            <p style="color: #495057;">Mã OTP này sẽ hết hạn sau 15 phút.</p>
            <div style="text-align: center; margin-top: 30px; color: #6c757d;">
              <p>Trân trọng,<br>Đội ngũ Ông Bà Dạy Hoa</p>
            </div>
          </div>
        `,
      };

      await strapi.plugins['email'].services.email.sendTemplatedEmail(
        {
          to: email,
          from: '<EMAIL>',
        },
        emailTemplate,
        {
          otp: otp
        }
      );

      return ctx.send({
        message: 'OTP đã được gửi thành công',
        success: true
      });
    } catch (err) {
      console.error('Error:', err);
      return ctx.throw(500, 'Có lỗi xảy ra khi xử lý yêu cầu');
    }
  },

  async verifyOTP(ctx) {
    try {
      const { email, otp, newPassword } = ctx.request.body;

      if (!email || !otp || !newPassword) {
        return ctx.badRequest('Thiếu thông tin bắt buộc');
      }

      const user = await strapi.db.query('plugin::users-permissions.user').findOne({
        where: {
          email,
          resetPasswordToken: otp,
          resetPasswordExpiration: { $gt: new Date() }
        }
      });

      if (!user) {
        return ctx.badRequest('Mã OTP không hợp lệ hoặc đã hết hạn');
      }

      const hashedPassword = await strapi.plugins['users-permissions'].services.user.hashPassword(newPassword);
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          resetPasswordToken: null,
          resetPasswordExpiration: null,
        }
      });

      return ctx.send({
        message: 'Mật khẩu đã được đặt lại thành công',
        success: true
      });
    } catch (err) {
      console.error('Error:', err);
      return ctx.throw(500, 'Có lỗi xảy ra khi đặt lại mật khẩu');
    }
  }
};