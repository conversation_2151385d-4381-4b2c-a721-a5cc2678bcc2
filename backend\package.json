{"name": "ong-ba-day-hoa-be", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "deploy": "strapi deploy", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-cloud": "5.9.0", "@strapi/plugin-users-permissions": "^5.9.0", "@strapi/provider-email-nodemailer": "^5.9.0", "@strapi/strapi": "^5.12.6", "axios": "^1.8.1", "bcrypt": "^5.1.1", "better-sqlite3": "11.3.0", "crypto": "^1.0.1", "form-data": "^4.0.2", "fs-extra": "^10.0.0", "knex": "^2.5.1", "mime-types": "^2.1.27", "mysql2": "^3.13.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-sso": "^1.0.2", "strapi-provider-email-brevo": "^1.0.4", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20.17.19", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}