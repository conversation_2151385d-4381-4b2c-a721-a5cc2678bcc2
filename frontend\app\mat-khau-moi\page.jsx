"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import strapi from "../api/strapi";
import TextField from "../../components/TextField";

const NewPasswordPage = () => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [email, setEmail] = useState("");
  const router = useRouter();

  useEffect(() => {
    // Lấy email từ sessionStorage
    const resetEmail = sessionStorage.getItem("resetEmail");
    if (!resetEmail) {
      // Nếu không có email, chuyển hướng về trang quên mật khẩu
      router.push("/quen-mat-khau");
      return;
    }

    setEmail(resetEmail);
  }, [router]);

  const validatePassword = (password) => {
    // Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ và số
    const passwordRegex =  /^(?=.*[A-Z])(?=.*\d).{8,}$/;
    return passwordRegex.test(password);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    // Kiểm tra mật khẩu
    if (!validatePassword(password)) {
      setError("Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số");
      return;
    }

    // Kiểm tra mật khẩu xác nhận
    if (password !== confirmPassword) {
      setError("Mật khẩu xác nhận không khớp");
      return;
    }

    try {
      setLoading(true);

      // Lấy thông tin người dùng từ sessionStorage
      const userDataString = sessionStorage.getItem("resetPasswordUser");
      if (!userDataString) {
        setError(
          "Không tìm thấy thông tin người dùng. Vui lòng thử lại từ đầu."
        );
        setLoading(false);
        return;
      }

      const userData = JSON.parse(userDataString);

      // Kiểm tra thời gian hết hạn (2 giờ)
      const currentTime = new Date().getTime();
      const storedTime = userData.timestamp;
      if (currentTime - storedTime > 2 * 60 * 60 * 1000) {
        setError(
          "Phiên đổi mật khẩu đã hết hạn. Vui lòng thực hiện lại từ đầu."
        );
        setLoading(false);
        return;
      }

      // Gọi API để đặt lại mật khẩu
      const response = await strapi.auth.resetPassword({
        email: userData.email,
        password: password,
        passwordConfirmation: confirmPassword,
      });

      if (response.success) {
        setSuccess(true);

        // Xóa dữ liệu từ sessionStorage vì đã hoàn thành quá trình
        sessionStorage.removeItem("resetPasswordUser");
        sessionStorage.removeItem("resetEmail");
        sessionStorage.removeItem("verifySource");

        // Chờ 2 giây trước khi chuyển hướng
        setTimeout(() => {
          router.push("/dang-nhap");
        }, 2000);
      } else {
        setError(
          response.message || "Không thể đặt lại mật khẩu. Vui lòng thử lại."
        );
      }
    } catch (err) {
      setError(
        err.response?.data?.message || "Có lỗi xảy ra. Vui lòng thử lại."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-screen">
      <div className="w-full bg-white flex items-center justify-center">
        <div className="max-w-[360px] w-full max-[390px]:px-4">
          <div className="flex flex-col items-center justify-center">
            <Image
              src="/images/Logo.png"
              alt="Logo"
              width={120}
              height={48}
              className="h-12 w-auto"
              onClick={() => router.push("/")}
            />
            <div className="text-3xl font-semibold text-[#181D27] mt-6">
              Tạo mật khẩu mới
            </div>
            <div className="text-base font-normal text-[#535862] mt-3 text-center">
              Nhập mật khẩu mới cho tài khoản {email}
            </div>
          </div>

          {success ? (
            <div className="mt-8 bg-[#F0FDF4] p-4 rounded-xl text-center">
              <svg
                className="mx-auto mb-2"
                width="48"
                height="48"
                viewBox="0 0 48 48"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24 4C12.96 4 4 12.96 4 24C4 35.04 12.96 44 24 44C35.04 44 44 35.04 44 24C44 12.96 35.04 4 24 4ZM20 34L10 24L12.82 21.18L20 28.34L35.18 13.16L38 16L20 34Z"
                  fill="#198C43"
                />
              </svg>
              <p className="text-[#198C43] font-medium text-lg">
                Đặt lại mật khẩu thành công!
              </p>
              <p className="text-sm text-[#198C43] mt-2">
                Đang chuyển hướng đến trang đăng nhập...
              </p>
            </div>
          ) : (
            <form className="mt-8" onSubmit={handleSubmit}>
              {error && (
                <div className="mb-6 bg-[#FEF3F2] text-sm text-[#414651] font-normal flex gap-4 p-4 rounded-xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_3814_10675)">
                      <path
                        d="M10.0001 6.66666V9.99999M10.0001 13.3333H10.0084M18.3334 9.99999C18.3334 14.6024 14.6025 18.3333 10.0001 18.3333C5.39771 18.3333 1.66675 14.6024 1.66675 9.99999C1.66675 5.39762 5.39771 1.66666 10.0001 1.66666C14.6025 1.66666 18.3334 5.39762 18.3334 9.99999Z"
                        stroke="#D92D20"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3814_10675">
                        <rect width="20" height="20" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  {error}
                </div>
              )}
              <div>
                <TextField
                  id="password"
                  name="password"
                  type="password"
                  label="Mật khẩu mới"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Nhập mật khẩu mới"
                  error={!!error}
                  helperText="Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số"
                />
              </div>
              <div className="mt-4">
                <TextField
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  label="Xác nhận mật khẩu"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Nhập lại mật khẩu mới"
                  error={!!error}
                />
              </div>
              <div className="mt-6">
                <button
                  type="submit"
                  disabled={loading || !password || !confirmPassword}
                  className={`w-full flex justify-center py-[10px] px-4 rounded-lg text-base font-semibold ${
                    loading || !password || !confirmPassword
                      ? "bg-[#F5F5F5] text-[#A4A7AE] cursor-not-allowed"
                      : "bg-[#299D55] text-[#FFF] hover:bg-[#198C43]"
                  }`}
                >
                  {loading ? "Đang xử lý..." : "Đặt lại mật khẩu"}
                </button>
              </div>
            </form>
          )}

          <div className="mt-8 text-center">
            <Link
              href="/dang-nhap"
              className="text-sm font-normal text-[#535862]"
            >
              ← Quay lại đăng nhập
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewPasswordPage;
