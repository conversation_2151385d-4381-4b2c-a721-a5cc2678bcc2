import { factories } from '@strapi/strapi';

export default {
  async createPaymentLink(ctx) {
    try {
      const { orderId, amount, description, returnUrl, cancelUrl } = ctx.request.body;
      const paymentService = strapi.service('api::payment.payment');
      
      const result = await paymentService.createPaymentLink({
        orderId,
        amount,
        description,
        returnUrl,
        cancelUrl
      });

      return ctx.send(result);
    } catch (error) {
      console.error('Create payment error:', error);
      return ctx.badRequest('Failed to create payment');
    }
  },

  async verifyPayment(ctx) {
    try {
      const { payosOrderId } = ctx.request.body;
      const paymentService = strapi.service('api::payment.payment');
      
      const result = await paymentService.verifyPayment(payosOrderId);
      return ctx.send(result);
    } catch (error) {
      console.error('Verify payment error:', error);
      return ctx.badRequest('Failed to verify payment');
    }
  },

  async webhook(ctx) {
    try {
      const { body, headers } = ctx.request;
      const paymentService = strapi.service('api::payment.payment');
      
      const result = await paymentService.handleWebhook(body, headers);
      return ctx.send(result);
    } catch (error) {
      console.error('Webhook error:', error);
      return ctx.badRequest('Webhook processing failed');
    }
  },

  async completePayment(ctx) {
    try {
      const { orderCode } = ctx.request.body;
      
      console.log('Completing payment, request body:', ctx.request.body);

      if (!orderCode) {
        return ctx.badRequest('OrderCode is required');
      }

      const result = await strapi
        .service('api::payment.payment')
        .completePayment(orderCode);

      console.log('Complete payment result:', result);

      return {
        data: result
      };

    } catch (error) {
      console.error('Complete payment controller error:', {
        message: error.message,
        stack: error.stack
      });
      return ctx.throw(500, error.message);
    }
  }
};