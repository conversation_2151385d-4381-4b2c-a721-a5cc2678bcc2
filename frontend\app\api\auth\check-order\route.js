import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import strapi from "../../strapi"; // Đ<PERSON><PERSON> bảo đường dẫn này đúng

export async function POST(request) {
  try {
    // Lấy userId từ request body
    const { userId } = await request.json();

    // Lấy token từ cookie
    const cookieStore = await cookies();
    const token = cookieStore.get("access_token")?.value;

    if (!token || !userId) {
      return NextResponse.json(
        { success: false, message: "Token hoặc userId bị thiếu." },
        { status: 401 }
      );
    }

    // Kiểm tra trạng thái đơn hàng

    const orderCheckResult = await strapi.auth.checkUserHasCompletedOrder(
      userId,
      token
    );

    // Cập nhật cookie
    if (orderCheckResult && orderCheckResult.hasCompletedOrder) {
      // Thêm kiểm tra orderCheckResult tồn tại
      const expiration = new Date();
      expiration.setDate(expiration.getDate() + 30);

      cookieStore.set("hasCompletedOrder", "true", {
        expires: expiration,
        path: "/",
        secure: process.env.NODE_ENV === "production", // Nên dùng biến môi trường cho secure
        sameSite: "lax",
      });

      if (orderCheckResult.latestOrder) {
        cookieStore.set(
          "completedOrderInfo",
          JSON.stringify(orderCheckResult.latestOrder),
          {
            expires: expiration,
            path: "/",
            secure: process.env.NODE_ENV === "production", // Nên dùng biến môi trường cho secure
            sameSite: "lax",
          }
        );
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Lỗi khi kiểm tra trạng thái đơn hàng:", error);
    // Trả về thông báo lỗi cụ thể hơn nếu có thể
    return NextResponse.json(
      {
        success: false,
        message: "Đã có lỗi xảy ra từ server.",
        error: error.message,
      },
      { status: 500 }
    );
  }
}
