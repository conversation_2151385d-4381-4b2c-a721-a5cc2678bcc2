"use client";

import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  Suspense,
} from "react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "../../context/AuthContext";
import Link from "next/link";
import { UserContext } from "../../context/UserProvider";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";

function VerifyOTPContent() {
  const [otp, setOtp] = useState(new Array(6).fill(""));
  const [activeOtpIndex, setActiveOtpIndex] = useState(-1);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const inputRefs = useRef([]);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { verifyOTP, sendOTP,updateUser2 } = useContext(UserContext);
  const from = searchParams.get("from");
  const email = searchParams.get("email");

  const handleSubmit = async (e) => {
    e.preventDefault();
    const otpCode = otp.join("");

    const response = await verifyOTP(email, otpCode);
    if (response.success) {
      if (from === "quen-mat-khau") {
        router.push("/mat-khau-moi");
      } else {
        router.push("/thong-tin-ca-nhan");
      }
      updateUser2({verified_otp: true});
    } else {
      setError(response.error || "Mã OTP không hợp lệ");
    }
  };

  const handleFocus = (index) => {
    setActiveOtpIndex(index);
  };

  const handleBlur = () => {
    setActiveOtpIndex(-1);
  };

  const handlePaste = (e, index) => {
    const pastedData = e.clipboardData.getData("Text");
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);
      newOtp.forEach((_, i) => {
        if (inputRefs.current[i]) {
          inputRefs.current[i].value = newOtp[i] || "";
        }
      });
      setActiveOtpIndex(newOtp.length);
    }
  };

  const handleChange = (e, index) => {
    const value = e.target.value;
    if (isNaN(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value.substring(value.length - 1);
    setOtp(newOtp);

    if (error) {
      setError("");
    }

    if (value && index < 5) {
      inputRefs.current[index + 1].focus();
      setActiveOtpIndex(index + 1);
    } else if (index === 5) {
      inputRefs.current[index].blur();
      setActiveOtpIndex(-1);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace") {
      const newOtp = [...otp];
      if (index > 0 && !otp[index]) {
        newOtp[index - 1] = "";
        setOtp(newOtp);
        inputRefs.current[index - 1].focus();
        setActiveOtpIndex(index - 1);
      } else if (index > 0) {
        newOtp[index] = "";
        setOtp(newOtp);
        inputRefs.current[index].focus();
        setActiveOtpIndex(index);
      }
    }
  };

  const handleResendOTP = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await sendOTP(email);
      if (response.success) {
        setError(""); // Xóa lỗi nếu có
      } else {
        setError(
          response.error || "Không thể gửi lại mã OTP. Vui lòng thử lại sau."
        );
      }
    } catch (err) {
      setError(
        err.message || "Không thể gửi lại mã OTP. Vui lòng thử lại sau."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-screen">
      <div className="w-full bg-[#FFFFFF] flex items-center justify-center">
        <div className="max-w-[360px] w-full max-[390px]:px-4">
          <div className="flex flex-col items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="80"
              height="80"
              viewBox="0 0 80 80"
              fill="none"
            >
              <path
                d="M71.8233 12.9541L41.1983 0.238902C40.4311 -0.0795355 39.5687 -0.0796918 38.8017 0.238902L8.17672 12.9541C7.01031 13.4384 6.25 14.5772 6.25 15.8402V31.2972C6.25 52.5805 19.1139 71.7253 38.8191 79.7683C39.5759 80.0772 40.4239 80.0772 41.1809 79.7683C60.8858 71.7255 73.75 52.5806 73.75 31.2972V15.8402C73.75 14.5772 72.9898 13.4384 71.8233 12.9541ZM67.5 31.2972C67.5 49.3902 56.875 66.0459 40 73.4806C23.5759 66.2445 12.5 49.8805 12.5 31.2972V17.9264L40 6.50859L67.5 17.9264V31.2972ZM36.5459 41.8306L49.9778 28.3989C51.1981 27.1786 53.1767 27.1784 54.3972 28.3989C55.6177 29.6194 55.6175 31.598 54.397 32.8183L38.7556 48.4597C37.535 49.6803 35.5564 49.6798 34.3363 48.4597L25.6028 39.7262C24.3823 38.5058 24.3823 36.5272 25.6028 35.3069C26.8233 34.0866 28.8019 34.0864 30.0222 35.3069L36.5459 41.8306Z"
                fill="url(#paint0_linear_1471_34812)"
              />
              <defs>
                <linearGradient
                  id="paint0_linear_1471_34812"
                  x1="6.25"
                  y1="1.52588e-05"
                  x2="73.75"
                  y2="1.52529e-05"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#299D55" />
                  <stop offset="1" stopColor="#45BF76" />
                </linearGradient>
              </defs>
            </svg>
            <div className="text-3xl text-center font-semibold text-[#181D27] mt-6">
              Xác thực mã OTP
            </div>
            <div className="text-base font-normal text-[#535862] mt-3 text-center px-8">
              Mã OTP đã được gửi đến "{email}". Nhập mã OTP để tiếp tục.
            </div>
            <form
              onSubmit={handleSubmit}
              className="mt-8 w-full flex flex-col items-center"
            >
              <div className="flex w-full gap-2 justify-between">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    type="text"
                    ref={(ref) => (inputRefs.current[index] = ref)}
                    value={otp[index]}
                    onChange={(e) => handleChange(e, index)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    onFocus={() => handleFocus(index)}
                    onBlur={handleBlur}
                    onPaste={(e) => handlePaste(e, index)}
                    className={`w-full h-[51px] rounded-[10px] text-center text-3xl font-medium ${
                      error
                        ? "border-[#FDA29B] border-2 text-[#D92D20]"
                        : "border-[#D5D7DA] border text-[#181D27]"
                    } focus:border-[#299D55] focus:outline-none focus:border-2`}
                    maxLength={1}
                  />
                ))}
              </div>
              {error && (
                <div className="text-[#D92D20] text-sm text-center mt-1">
                  {error}
                </div>
              )}
              <button
                type="submit"
                disabled={otp.some((digit) => digit === "") || loading}
                className={`w-full max-w-[360px] justify-center items-center flex py-[10px] px-4 mt-[20px] rounded-lg font-semibold ${
                  otp.some((digit) => digit === "") || loading
                    ? "cursor-not-allowed text-[#A4A7AE] border-[#E9EAEB] bg-[#F5F5F5]"
                    : "bg-[#299D55] text-[#FFFFFF]"
                }`}
              >
                {loading ? "Đang xử lý..." : "Xác nhận"}
              </button>
            </form>
            <div className="mt-6 text-center">
              <p className="text-[#535862]">
                Bạn chưa nhận được mã OTP?{" "}
                <button
                  onClick={handleResendOTP}
                  disabled={loading}
                  className="text-[#299D55] font-semibold disabled:opacity-50"
                >
                  Gửi lại
                </button>
              </p>
            </div>
            {/*<a*/}
            {/*  onClick={() => router.back()}*/}
            {/*  className="mt-6 text-[#535862] flex items-center justify-center text-center"*/}
            {/*>*/}
            {/*  ← Quay lại*/}
            {/*</a>*/}
          </div>
        </div>
      </div>
    </div>
  );
}

const VerifyOTPPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyOTPContent />
    </Suspense>
  );
};

export default VerifyOTPPage;
