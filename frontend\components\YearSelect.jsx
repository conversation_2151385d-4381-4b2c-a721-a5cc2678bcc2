"use client";
import React, { useState, useEffect, useRef } from "react";

const YearSelect = ({ onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedYear, setSelectedYear] = useState("");
  const dropdownRef = useRef(null);

  // Tính toán năm sinh cho học sinh cấp 3 (15-18 tuổi)
  const currentYear = new Date().getFullYear();
  const startYear = currentYear - 14; // Học sinh 15 tuổi
  const years = Array.from({ length: 5 }, (_, i) => startYear - i); // Lấy 5 năm liên tiếp

  const handleYearSelect = (year) => {
    setSelectedYear(year);
    setIsOpen(false);
    if (onChange) {
      onChange(year);
    }
  };

  // Thêm effect để xử lý click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative w-full bg-[#FFFFFF]" ref={dropdownRef}>
      <button
        type="button"
        className="w-full border border-[#E9EAEB] rounded-lg px-[14px] py-[10px] text-left flex items-center justify-between focus:outline-none focus:border-2 focus:border-[#45BF76]"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-[#181D27] text-base font-normal">
          {selectedYear || "Chọn năm sinh"}
        </span>
        <div className="flex items-center">
          <svg
            className={`w-4 h-4 transition-transform ${
              isOpen ? "rotate-180" : ""
            }`}
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              d="M5 7.5L10 12.5L15 7.5"
              stroke="#717680"
              strokeWidth="1.66667"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="absolute bg-[#FFFFFF] z-10 w-full mt-1 border border-[#E9EAEB] rounded-lg max-h-60 overflow-auto shadow-lg">
          {years.map((year) => (
            <button
              key={year}
              className={`w-full flex justify-between px-4 py-2 text-left hover:bg-[#FAFAFA] focus:outline-none ${
                selectedYear === year ? "bg-[#FAFAFA]" : ""
              }`}
              onClick={() => handleYearSelect(year)}
            >
              {year}
              {selectedYear === year && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <path
                    d="M16.6668 5L7.50016 14.1667L3.3335 10"
                    stroke="#299D55"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default YearSelect;
