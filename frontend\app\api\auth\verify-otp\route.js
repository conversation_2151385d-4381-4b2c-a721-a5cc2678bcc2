import { NextResponse } from 'next/server'
import strapi from '../../strapi'

export async function POST(request) {
    try {
        // Lấy dữ liệu từ body
        const { email, code } = await request.json()

        if (!email || !code) {
            return NextResponse.json(
                { success: false, error: 'Email và mã xác thực là bắt buộc' },
                { status: 400 }
            )
        }

        // Gọi API xác thực OTP từ Strapi
        const response = await strapi.sendOTP.verify(email, code)

        // Nếu xác thực OTP thành công
        if (response.success) {
            return NextResponse.json({
                success: true,
                message: '<PERSON><PERSON><PERSON> thực thành công'
            })
        }

        // Trả về lỗi nếu không thành công
        return NextResponse.json(
            { success: false, error: response.error || 'Mã xác thực không đúng' },
            { status: 400 }
        )
    } catch (error) {
        console.error('Lỗi xác thực OTP:', error)
        return NextResponse.json(
            { success: false, error: '<PERSON><PERSON><PERSON> thực thất bại. <PERSON>ui lòng thử lại sau.' },
            { status: 500 }
        )
    }
} 