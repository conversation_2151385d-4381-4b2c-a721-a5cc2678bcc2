"use client";

import React, { useState, useEffect, useCallback, useContext } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import userimage from "../public/images/user-image.png";
import mobilelogo from "../public/images/white-logo.png";
import { UserContext } from "../context/UserProvider";
import { useCookies } from "react-cookie";
import MobileMenu from "./MobileMenu";
import ProfileModal from "./dashboard/ProfileModal";
import PasswordChangeModal from "./dashboard/PasswordChangeModal";
import ConfirmDialog from "./ui/ConfirmDialog";
import strapi from "../app/api/strapi";
import { getUser, isAuthenticated as checkIsAuthenticated } from "../utils/cookieHelper";
import { useToast } from "../context/ToastContext";

// Component tách riêng để tránh code duplication
const LoginButtonPlaceholder = ({ withBackground = false }) => (
  <div className={`group rounded-lg hover:bg-[#299D55] transition duration-200 ${withBackground ? 'bg-[#000000]/30' : ''}`}>
    <div className="text-[#FFFFFF] text-base flex items-center gap-2 font-bold px-3 py-[10px]">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 14 14"
        fill="none"
      >
        <path
          d="M2.625 12.25C2.625 12.25 1.75 12.25 1.75 11.375C1.75 10.5 2.625 7.875 7 7.875C11.375 7.875 12.25 10.5 12.25 11.375C12.25 12.25 11.375 12.25 11.375 12.25H2.625ZM7 7C7.69619 7 8.36387 6.72344 8.85616 6.23116C9.34844 5.73887 9.625 5.07119 9.625 4.375C9.625 3.67881 9.34844 3.01113 8.85616 2.51884C8.36387 2.02656 7.69619 1.75 7 1.75C6.30381 1.75 5.63613 2.02656 5.14384 2.51884C4.65156 3.01113 4.375 3.67881 4.375 4.375C4.375 5.07119 4.65156 5.73887 5.14384 6.23116C5.63613 6.72344 6.30381 7 7 7Z"
          fill="white"
        />
      </svg>
      Đăng nhập
    </div>
  </div>
);

const Header = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isPasswordChangeModalOpen, setIsPasswordChangeModalOpen] =
    useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [liveStreamCourses, setLiveStreamCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isMounted, setIsMounted] = useState(false);

  const router = useRouter();
  const { user, logout, isAuthenticated, token, logoutLoading } = useContext(UserContext);
  const [cookies] = useCookies([]);
  const toast = useToast();

  // Fix hydration: only render conditional content after mount
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Kiểm tra đăng nhập bằng cả cookie và context
  const isLoggedIn = useCallback(() => {
    return isMounted && isAuthenticated;
  }, [isAuthenticated, user, isMounted]);

  // Đồng bộ context và cookie khi component mount
  useEffect(() => {
    const syncAuthState = async () => {
      if (cookies.auth_token && !user) {
        window.location.reload();
      }
    };

    syncAuthState();
  }, [cookies.auth_token, user]);

  const getUserData = useCallback(() => {
    if (!user) {
      return null;
    }

    const getLastName = (fullname) => {
      const nameParts = fullname?.trim().split(" ") || ["User"];
      return nameParts[nameParts.length - 1];
    };

    const userData = {
      ...user,
      name: user.fullname ? getLastName(user.fullname) : "User",
      email: user.email || "<EMAIL>",
      phone: user.phone || "",
      imageUrl: user.image?.[0]?.url
        ? process.env.NEXT_PUBLIC_STRAPI_URL + user.image[0].url
        : null,
    };

    return userData;
  }, [user]);

  useEffect(() => {
    const fetchLiveStreamCourses = async () => {
      try {
        const response = await strapi.grade.getGrade();
        setLiveStreamCourses(response.data || []);
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu khóa học Livestream:", error);
      }
    };

    fetchLiveStreamCourses();
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      const result = await logout(true); // Request confirmation

      if (result.needsConfirmation) {
        setShowLogoutConfirm(true);
        return;
      } else if (!result.success) {
        // Handle logout failure
        toast.error("Đăng xuất thất bại", "Vui lòng thử lại");
        return;
      }

      // Close menus after successful logout
      setIsMobileMenuOpen(false);
      setIsUserMenuOpen(false);
    } catch (error) {
      console.error("Lỗi khi đăng xuất:", error);
      toast.error("Có lỗi xảy ra khi đăng xuất", "Vui lòng thử lại");
    }
  }, [logout, toast]);

  const handleConfirmLogout = useCallback(async () => {
    try {
      await logout(false); // Skip confirmation, perform actual logout
      setShowLogoutConfirm(false);
      setIsMobileMenuOpen(false);
      setIsUserMenuOpen(false);
    } catch (error) {
      console.error("Lỗi khi đăng xuất:", error);
      toast.error("Có lỗi xảy ra khi đăng xuất", "Vui lòng thử lại");
    }
  }, [logout, toast]);

  const handleCancelLogout = useCallback(() => {
    setShowLogoutConfirm(false);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (typeof window !== "undefined") {
        const scrollTop = window.scrollY;
        setIsScrolled(scrollTop > 100);
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener("scroll", handleScroll);
      return () => {
        window.removeEventListener("scroll", handleScroll);
      };
    }
  }, []);

  // Mở menu mobile nếu người dùng đã đăng nhập
  useEffect(() => {
    if (isLoggedIn()) {
      setIsMobileMenuOpen(false);
    }
  }, []);

  const toggleDropdown = () => {
    setIsDropdownOpen((prev) => !prev);
  };

  // New function to handle course selection
  const handleCourseSelect = (course) => {
    // Logic to handle course selection
    console.log(course); // Replace with actual logic
    setIsDropdownOpen(false); // Close dropdown after selection
  };

  // Effect to handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdown = document.getElementById("dropdown"); // Assuming you give an id to the dropdown
      if (dropdown && !dropdown.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prev) => !prev);
    if (typeof document !== "undefined") {
      document.body.classList.toggle("overflow-hidden", !isMobileMenuOpen);
    }
  };

  const handleOpenProfile = () => {
    setIsProfileModalOpen(true);
  };

  const handleCloseProfile = () => {
    setIsProfileModalOpen(false);
  };

  const handleOpenPasswordChange = () => {
    setIsPasswordChangeModalOpen(true);
  };

  const handleClosePasswordChange = () => {
    setIsPasswordChangeModalOpen(false);
  };

  return (
    <header
      className={`md:p-6  flex w-full z-50 bg-[#FFFFFF] ${
        isScrolled
          ? "fixed top-0 left-0 w-full md:justify-between md:bg-[#FFFFFF] md:border-b md:border-[#D5D7DA] justify-center items-center"
          : "justify-center items-center bg-[#FFFFFF] "
      }`}
    >
      <div
        className={`container flex mx-auto justify-between max-w-[1440px] items-center max-sm:bg-[#FFFFFF]  max-md:py-3 max-md:px-4 max-md:border-b max-md:border-[#E9EAEB] ${
          isScrolled ? "md:bg-transparent" : ""
        }`}
      >
        {/* Logo */}
        {(!isScrolled ||
          (typeof window !== "undefined" && window.innerWidth < 960)) && (
          <div className="flex-shrink-0 md:block hidden">
            <Image
              src="/images/homepage/logo.png"
              alt="Logo"
              className="cursor-pointer object-cover"
              width={130}
              height={48}
              onClick={() => router.push("/")}
            />
          </div>
        )}
        <div className="flex-shrink-0 md:hidden block">
          <Image
            src="/images/homepage/logo.png"
            alt="Logo"
            className="cursor-pointer object-cover"
            width={87}
            height={32}
          />
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden block">
          <div className="flex items-center">
            <button
              onClick={toggleMobileMenu}
              className="text-[$FFFFFF] focus:outline-none"
            >
              {isMobileMenuOpen ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M18 6L6 18M6 6L18 18"
                    stroke="#414651"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M3 12H21M3 6H21M3 18H21"
                    stroke="#414651"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Navigation (Desktop) */}
        <nav
          className={`hidden md:flex bg-[#000000]/30 rounded-lg items-center ${
            isScrolled ? "mx-auto backdrop-blur" : "mx-6"
          }`}
        >
          <div className="group rounded-lg hover:bg-[#299D55] transition duration-200 px-3 py-[10px]">
            <Link
              href="/hoc-tai-trung-tam"
              className="text-[#FFFFFF] text-base block font-bold group-hover:font-bold"
            >
              Học tại Trung tâm
            </Link>
          </div>
          <div className="relative group rounded-lg hover:bg-[#299D55] transition duration-200 px-3 py-[10px]">
            <button
              type="button"
              className="text-[#FFFFFF] text-base font-bold group-hover:font-bold flex items-center space-x-1"
              onClick={toggleDropdown}
              aria-expanded={isDropdownOpen}
            >
              <span>Khoá học Livestream</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="25"
                height="24"
                viewBox="0 0 25 24"
                fill="none"
              >
                <path
                  d="M6.5 9L12.5 15L18.5 9"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            {isDropdownOpen && (
              <div
                id="dropdown"
                className="absolute border border-[rgba(0,0,0,0.15)] w-[280px] left-0 mt-3 bg-[#FFF] rounded-lg z-10 p-2 "
              >
                {liveStreamCourses.map((course) => (
                  <Link
                    className="p-3 text-base font-semibold text-[#1E2125] hover:bg-[#FAFAFA] hover:rounded-lg flex items-center"
                    key={course.id}
                    href={`/khoa-hoc/${course.course.slug}`}
                    onClick={() => handleCourseSelect(course)}
                  >
                    {course.title}
                  </Link>
                ))}
              </div>
            )}
          </div>
          <div className="group rounded-lg hover:bg-[#299D55] transition duration-200 px-3 py-[10px]">
            <Link
              href="/bai-viet"
              className="text-[#FFFFFF] text-base block font-bold group-hover:font-bold"
            >
              Bài viết
            </Link>
          </div>

          {/* Menu người dùng khi cuộn */}
          {isScrolled && (
            <div className="hidden md:block ">
              {!isMounted ? (
                // Placeholder để tránh hydration mismatch
                <LoginButtonPlaceholder />
              ) : isLoggedIn() ? (
                <div className="relative ">
                  <button
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    className="group rounded-lg hover:bg-[#299D55] transition duration-200"
                  >
                    <div className="text-[#FFFFFF] text-base flex  items-center gap-2 font-bold group-hover:font-bold px-3 py-[10px]">
                      {getUserData()?.imageUrl ? (
                        <Image
                          src={getUserData()?.imageUrl}
                          alt="User avatar"
                          className="w-[20px] h-[20px] rounded-full object-cover"
                          width={20}
                          height={20}
                        />
                      ) : (
                        <div className="w-[20px] h-[20px] rounded-full bg-[#299D55] text-[#FFFFFF] flex items-center justify-center text-xs font-medium">
                          {getUserData()?.name?.charAt(0)?.toUpperCase() || "U"}
                        </div>
                      )}
                      {getUserData()?.name}
                    </div>
                  </button>
                  {isUserMenuOpen && (
                    <div className="absolute right-0 mt-2 w-[240px] bg-[#FFFFFF] border-[#E9EAEB] border rounded-2xl shadow-lg z-50">
                      <div className="p-2 ">
                        <button
                          onClick={handleOpenProfile}
                          className="flex gap-3 p-3 text-base text-[#181D27] font-normal hover:bg-[#FAFAFA] rounded-lg"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M12 15C8.8299 15 6.01077 16.5306 4.21597 18.906C3.82968 19.4172 3.63653 19.6728 3.64285 20.0183C3.64773 20.2852 3.81533 20.6219 4.02534 20.7867C4.29716 21 4.67384 21 5.4272 21H18.5727C19.3261 21 19.7028 21 19.9746 20.7867C20.1846 20.6219 20.3522 20.2852 20.3571 20.0183C20.3634 19.6728 20.1703 19.4172 19.784 18.906C17.9892 16.5306 15.17 15 12 15Z"
                              stroke="#717680"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M12 12C14.4853 12 16.5 9.98528 16.5 7.5C16.5 5.01472 14.4853 3 12 3C9.51469 3 7.49997 5.01472 7.49997 7.5C7.49997 9.98528 9.51469 12 12 12Z"
                              stroke="#717680"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          Thông tin cá nhân
                        </button>
                        <button
                          onClick={handleOpenPasswordChange}
                          className="flex gap-3 p-3 text-base text-[#181D27] font-normal hover:bg-[#FAFAFA] rounded-lg"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M3 6C3 4.93913 3.42143 3.92172 4.17157 3.17157C4.92172 2.42143 5.93913 2 7 2H20C20.5523 2 21 2.44772 21 3V21C21 21.5523 20.5523 22 20 22H7C5.93913 22 4.92172 21.5786 4.17157 20.8284C3.42143 20.0783 3 19.0609 3 18V6ZM5 14.535C5.60816 14.1842 6.29794 13.9997 7 14H19V4H17V8.99981C17 9.77711 16.152 10.2572 15.4855 9.8573L13.5 8.666L11.5145 9.8573C10.848 10.2572 10 9.7771 10 8.99981V4H7C6.46957 4 5.96086 4.21071 5.58579 4.58579C5.21071 4.96086 5 5.46957 5 6V14.535ZM19 16H7C6.46957 16 5.96086 16.2107 5.58579 16.5858C5.21071 16.9609 5 17.4696 5 18C5 18.5304 5.21071 19.0391 5.58579 19.4142C5.96086 19.7893 6.46957 20 7 20H19V16ZM15 4H12V7.234L13.5 6.334L15 7.234V4Z"
                              fill="#717680"
                            />
                          </svg>
                          Đổi mật khẩu
                        </button>

                        <button
                          onClick={() => {
                            handleLogout();
                            setIsUserMenuOpen(false);
                          }}
                          className="flex gap-3 w-full text-left p-3 text-base text-[#181D27] font-normal hover:bg-[#FAFAFA] rounded-lg"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                          >
                            <path
                              d="M15 3H16.2C17.8802 3 18.7202 3 19.362 3.32698C19.9265 3.6146 20.3854 4.07354 20.673 4.63803C21 5.27976 21 6.11985 21 7.8V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H15M10 7L15 12M15 12L10 17M15 12L3 12"
                              stroke="#717680"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          Đăng xuất
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="group rounded-lg hover:bg-[#299D55] transition duration-200 ">
                  <Link
                    href="/dang-nhap"
                    className="text-[#FFFFFF] text-base flex items-center gap-2 font-bold group-hover:font-bold px-3 py-[10px]"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 14 14"
                      fill="none"
                    >
                      <path
                        d="M2.625 12.25C2.625 12.25 1.75 12.25 1.75 11.375C1.75 10.5 2.625 7.875 7 7.875C11.375 7.875 12.25 10.5 12.25 11.375C12.25 12.25 11.375 12.25 11.375 12.25H2.625ZM7 7C7.69619 7 8.36387 6.72344 8.85616 6.23116C9.34844 5.73887 9.625 5.07119 9.625 4.375C9.625 3.67881 9.34844 3.01113 8.85616 2.51884C8.36387 2.02656 7.69619 1.75 7 1.75C6.30381 1.75 5.63613 2.02656 5.14384 2.51884C4.65156 3.01113 4.375 3.67881 4.375 4.375C4.375 5.07119 4.65156 5.73887 5.14384 6.23116C5.63613 6.72344 6.30381 7 7 7Z"
                        fill="white"
                      />
                    </svg>
                    Đăng nhập
                  </Link>
                </div>
              )}
            </div>
          )}
        </nav>

        {/* Menu người dùng khi không cuộn */}
        {!isScrolled && (
          <div className="hidden md:block">
            {!isMounted ? (
              // Placeholder để tránh hydration mismatch
              <LoginButtonPlaceholder withBackground={true} />
            ) : isLoggedIn() ? (
              <div className="relative mt-2">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="group rounded-lg hover:bg-[#299D55] transition duration-200 bg-[#000000]/30"
                >
                  <div className="text-[#FFFFFF] text-base flex items-center gap-2 font-bold group-hover:font-bold px-3 py-[10px]">
                    {getUserData()?.imageUrl ? (
                      <Image
                        src={getUserData()?.imageUrl}
                        alt="User avatar"
                        className="w-[20px] h-[20px] rounded-full object-cover"
                        width={20}
                        height={20}
                      />
                    ) : (
                      <Image
                        src={userimage}
                        alt="User avatar"
                        className="w-[20px] h-[20px] rounded-full object-cover"
                        width={20}
                        height={20}
                      />
                    )}
                    Xin chào, {getUserData()?.name}
                  </div>
                </button>
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-[240px] bg-[#FFFFFF] border-[#E9EAEB] border rounded-2xl shadow-lg z-50">
                    <div className="p-2 ">
                      <button
                        onClick={handleOpenProfile}
                        className="flex gap-3 p-3 text-base text-[#181D27] font-normal hover:bg-[#FAFAFA] rounded-lg"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <path
                            d="M12 15C8.8299 15 6.01077 16.5306 4.21597 18.906C3.82968 19.4172 3.63653 19.6728 3.64285 20.0183C3.64773 20.2852 3.81533 20.6219 4.02534 20.7867C4.29716 21 4.67384 21 5.4272 21H18.5727C19.3261 21 19.7028 21 19.9746 20.7867C20.1846 20.6219 20.3522 20.2852 20.3571 20.0183C20.3634 19.6728 20.1703 19.4172 19.784 18.906C17.9892 16.5306 15.17 15 12 15Z"
                            stroke="#717680"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M12 12C14.4853 12 16.5 9.98528 16.5 7.5C16.5 5.01472 14.4853 3 12 3C9.51469 3 7.49997 5.01472 7.49997 7.5C7.49997 9.98528 9.51469 12 12 12Z"
                            stroke="#717680"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        Thông tin cá nhân
                      </button>
                      <button
                        onClick={handleOpenPasswordChange}
                        className="flex gap-3 p-3 text-base text-[#181D27] font-normal hover:bg-[#FAFAFA] rounded-lg"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <path
                            d="M3 6C3 4.93913 3.42143 3.92172 4.17157 3.17157C4.92172 2.42143 5.93913 2 7 2H20C20.5523 2 21 2.44772 21 3V21C21 21.5523 20.5523 22 20 22H7C5.93913 22 4.92172 21.5786 4.17157 20.8284C3.42143 20.0783 3 19.0609 3 18V6ZM5 14.535C5.60816 14.1842 6.29794 13.9997 7 14H19V4H17V8.99981C17 9.77711 16.152 10.2572 15.4855 9.8573L13.5 8.666L11.5145 9.8573C10.848 10.2572 10 9.7771 10 8.99981V4H7C6.46957 4 5.96086 4.21071 5.58579 4.58579C5.21071 4.96086 5 5.46957 5 6V14.535ZM19 16H7C6.46957 16 5.96086 16.2107 5.58579 16.5858C5.21071 16.9609 5 17.4696 5 18C5 18.5304 5.21071 19.0391 5.58579 19.4142C5.96086 19.7893 6.46957 20 7 20H19V16ZM15 4H12V7.234L13.5 6.334L15 7.234V4Z"
                            fill="#717680"
                          />
                        </svg>
                        Đổi mật khẩu
                      </button>

                      <button
                        onClick={() => {
                          handleLogout();
                          setIsUserMenuOpen(false);
                        }}
                        className="flex gap-3 w-full text-left p-3 text-base text-[#181D27] font-normal hover:bg-[#FAFAFA] rounded-lg"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <path
                            d="M15 3H16.2C17.8802 3 18.7202 3 19.362 3.32698C19.9265 3.6146 20.3854 4.07354 20.673 4.63803C21 5.27976 21 6.11985 21 7.8V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H15M10 7L15 12M15 12L10 17M15 12L3 12"
                            stroke="#717680"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        Đăng xuất
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="group rounded-lg hover:bg-[#299D55] transition duration-200 bg-[#000000]/30">
                <Link
                  href="/dang-nhap"
                  className="text-[#FFFFFF] text-base flex items-center gap-2 font-bold group-hover:font-bold px-3 py-[10px]"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 14 14"
                    fill="none"
                  >
                    <path
                      d="M2.625 12.25C2.625 12.25 1.75 12.25 1.75 11.375C1.75 10.5 2.625 7.875 7 7.875C11.375 7.875 12.25 10.5 12.25 11.375C12.25 12.25 11.375 12.25 11.375 12.25H2.625ZM7 7C7.69619 7 8.36387 6.72344 8.85616 6.23116C9.34844 5.73887 9.625 5.07119 9.625 4.375C9.625 3.67881 9.34844 3.01113 8.85616 2.51884C8.36387 2.02656 7.69619 1.75 7 1.75C6.30381 1.75 5.63613 2.02656 5.14384 2.51884C4.65156 3.01113 4.375 3.67881 4.375 4.375C4.375 5.07119 4.65156 5.73887 5.14384 6.23116C5.63613 6.72344 6.30381 7 7 7Z"
                      fill="white"
                    />
                  </svg>
                  Đăng nhập
                </Link>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Mobile Menu (Slide-in) */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        user={user}
        isAuthenticated={isAuthenticated}
        handleLogout={handleLogout}
      />
      <ProfileModal isOpen={isProfileModalOpen} onClose={handleCloseProfile} />
      <PasswordChangeModal
        isOpen={isPasswordChangeModalOpen}
        onClose={handleClosePasswordChange}
      />
      <ConfirmDialog
        isOpen={showLogoutConfirm}
        onClose={handleCancelLogout}
        onConfirm={handleConfirmLogout}
        title="Xác nhận đăng xuất"
        message="Bạn có chắc chắn muốn đăng xuất khỏi tài khoản không?"
        confirmText="Đăng xuất"
        cancelText="Hủy"
        type="warning"
        loading={logoutLoading}
      />
    </header>
  );
};

export default Header;
