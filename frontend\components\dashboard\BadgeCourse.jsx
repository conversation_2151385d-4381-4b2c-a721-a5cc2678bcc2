"use client";

import React from "react";

const BadgeCourse = ({ plan = "", type = "", size = "" }) => {
  // Plan text theo loại
  const planText = plan === "Full" ? "Gói 1 năm" : "<PERSON><PERSON><PERSON> học thử";

  // <PERSON><PERSON><PERSON> sắc theo type
  const typeStyles = {
    Default: {
      background: "bg-[#DCFAEC]",
      text: "text-[#114721]",
    },
    Neutral: {
      background: "bg-[#FFFFFF] opacity-70",
      text: "text-[#114721]",
    },
  };

  // <PERSON><PERSON>ch thước theo size
  const sizeStyles = {
    sm: {
      padding: "px-1.5 ",
      font: "text-[10px] leading-[16px] font-medium",
      rounded: "rounded-[9px]",
    },
    lg: {
      padding: "px-2 py-0.5",
      font: "text-xs leading-[18px] font-medium",
      rounded: "rounded-[9px]",
    },
  };

  // Lấy style cho từng loại
  const bgClass = typeStyles[type]?.background || typeStyles.Default.background;
  const textClass = typeStyles[type]?.text || typeStyles.Default.text;
  const paddingClass = sizeStyles[size]?.padding || sizeStyles.sm.padding;
  const fontClass = sizeStyles[size]?.font || sizeStyles.sm.font;
  const roundedClass = sizeStyles[size]?.rounded || sizeStyles.sm.rounded;

  return (
    <span
      className={`inline-flex items-center justify-center ${bgClass} ${textClass} ${paddingClass} ${fontClass} ${roundedClass}`}
      aria-label={planText}
    >
      {planText}
    </span>
  );
};

export default BadgeCourse;
