"use client";

import Image from "next/image";
import Button from "./Button";
import CountdownTimer from "./CountdownTimer";

const CtaSection = ({ show = true }) => {
  if (!show) return null;

  return (
    <section className="w-full py-16">
      <div className="max-w-[1200px] mx-auto px-4">
        <div className="w-full bg-[#F0FFF7] border border-[#E9EAEB] rounded-xl p-6 md:p-20 flex flex-col md:flex-row items-center gap-4 md:gap-6 relative overflow-hidden">
          {/* Left Content */}
          <div className="flex-1 z-10 flex flex-col gap-5">
            <div>
              <h2 className="text-2xl md:text-5xl text-center md:text-start font-semibold text-[#114721] mb-2 leading-tight">
                Đừng bỏ lỡ cơ hội!
              </h2>
              <p className="text-lg text-center md:text-start text-[#535862]">
                Chỉ còn 2 tháng - Đ<PERSON> để thay đổi với phương pháp đúng!
              </p>
            </div>

            {/* Countdown Timer */}
            <div className="my-3 md:my-4">
              <CountdownTimer targetDate="2025-07-01T00:00:00" />
            </div>

            {/* CTA Button */}
            <div className="w-full flex sm:justify-center md:justify-start">
              <Button
                variant="primary"
                className="w-full sm:w-auto px-5 py-3 shadow-md"
              >
                Đăng ký ngay
              </Button>
            </div>
          </div>

          {/* Background Image - Right Side */}
          <div className="w-full md:w-1/3 flex items-center justify-center">
            <div className="relative w-[200px] h-[200px] sm:w-[300px] sm:h-[300px]">
              <Image
                src="/images/homepage/footer-img.png"
                alt="CTA Background"
                fill
                style={{ objectFit: "contain" }}
                sizes="(max-width: 768px) 200px, 300px"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
