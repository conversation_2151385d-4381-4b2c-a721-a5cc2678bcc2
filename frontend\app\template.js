// frontend/app/template.js
// File này sẽ chỉ áp dụng cho route '/' (trang chủ) nếu page.js của trang chủ là Client Component.
// Nếu page.js của trang chủ được chuyển thành Server Component, metadata nên đặt trực tiếp trong page.js.

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const ogImageUrl = `${siteUrl}/images/metadata-img/homepage-og.jpg`;

export const metadata = {
  title: 'Ông Ba Dạy Hóa - Trang Chủ | Học Hóa Online Chất Lượng Cao',
  description: 'Khám phá các khóa học Hóa online THPT, phương pháp học tập trực quan, thí nghiệm thực tế và lộ trình cá nhân hóa cùng Ông Ba Dạy Hóa.',
  alternates: {
    canonical: siteUrl,
  },
  openGraph: {
    title: 'Ông Ba Dạy Hóa - Trang Chủ | Học Hóa Online Chất Lượng Cao',
    description: 'Khám phá các khóa học Hóa online THPT, phương pháp học tập trực quan, thí nghiệm thực tế và lộ trình cá nhân hóa cùng Ông Ba Dạy Hóa.',
    url: siteUrl,
    siteName: 'Ông Ba Dạy Hóa', // siteName có thể đặt ở RootLayout nếu muốn áp dụng toàn cục
    images: [
      {
        url: ogImageUrl,
        width: 1200,
        height: 630,
        alt: 'Trang Chủ Ông Ba Dạy Hóa',
      },
    ],
    locale: 'vi_VN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ông Ba Dạy Hóa - Trang Chủ | Học Hóa Online Chất Lượng Cao',
    description: 'Khám phá các khóa học Hóa online THPT...',
    images: [ogImageUrl],
  },
};

export default function HomePageTemplate({ children }) {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  const websiteJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Ông Ba Dạy Hóa',
    url: siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${siteUrl}/tim-kiem?q={search_term_string}`, // Giả sử có trang tìm kiếm
      'query-input': 'required name=search_term_string',
    },
    logo: `${siteUrl}/images/Logo.png`, // Đảm bảo Logo.png có trong public/images
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteJsonLd) }}
      />
      {children}
    </>
  );
}