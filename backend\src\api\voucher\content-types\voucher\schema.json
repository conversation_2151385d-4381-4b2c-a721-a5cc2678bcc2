{"kind": "collectionType", "collectionName": "vouchers", "info": {"singularName": "voucher", "pluralName": "vouchers", "displayName": "Voucher"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "code": {"type": "string", "unique": true}, "time_start": {"type": "datetime"}, "time_end": {"type": "datetime"}, "discount_percent": {"type": "integer"}, "discount_amount": {"type": "decimal", "default": 0}, "max_uses": {"type": "integer", "default": 100}, "current_uses": {"type": "integer", "default": 0}, "allowed_emails": {"type": "json", "comment": "<PERSON><PERSON> s<PERSON>ch email đ<PERSON><PERSON><PERSON> phép sử dụng voucher"}, "used_emails": {"type": "json", "comment": "<PERSON><PERSON> s<PERSON>ch email đã sử dụng voucher", "default": []}, "courses": {"type": "relation", "relation": "manyToMany", "target": "api::course.course", "inversedBy": "vouchers"}}}