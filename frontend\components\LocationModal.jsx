"use client";
import { useRouter } from "next/navigation";
import Image from "next/image";
import React from "react";
import map from "../public/images/locationmodal.png";
import Button from "./Button";

const LocationModal = ({ onClose }) => {
  const router = useRouter();

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="fixed inset-0 bg-[#000000] bg-opacity-50 backdrop-blur-[8px] flex items-center justify-center z-50">
      <div className="bg-[#FFFFFF] rounded-xl sm:p-6 p-[20px] w-full md:max-w-[489px] sm:max-w-[452px] mx-4">
        <div className="flex flex-col items-center">
          <div className="relative w-full h-[200px] mb-4">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2189.1106177694865!2d108.06061090065931!3d12.696351210008894!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3171f765337fd1ab%3A0x15d8cbfa3e3273dd!2zw5RuZyBCYSBE4bqheSBIb8Oh!5e0!3m2!1svi!2s!4v1746364468048!5m2!1svi!2s"
              style={{ border: 0 }}
              allowFullScreen=""
              height="200"
              width="100%"
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              className="rounded-xl"
            ></iframe>
          </div>
          <div className="w-12 h-12 bg-[#E6F5EB] rounded-full flex items-center justify-center sm:mt-6 mt-[20px] mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
                stroke="#299D55"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M12 22C16 18 20 14.4183 20 10C20 5.58172 16.4183 2 12 2C7.58172 2 4 5.58172 4 10C4 14.4183 8 18 12 22Z"
                stroke="#299D55"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>

          <h2 className="text-lg text-[#181D27] font-semibold text-center mb-1 ">
            Địa chỉ Trung tâm
          </h2>

          <p className="text-center text-sm text-[#535862] mb-6">
            21 Đ. Chu Văn An, Tân An, Buôn Ma Thuột, Đắk Lắk, Việt Nam
          </p>

          <p className="text-center text-sm text-[#535862] sm:mb-8 mb-6">
            Bạn hãy cân nhắc địa chỉ trước khi đăng ký nhé!
          </p>

          <div className="flex flex-col-reverse sm:flex-row gap-3 w-full">
            <Button
              variant="secondaryGray"
              onClick={handleCancel}
              className="flex-1 sm:flex-2"
            >
              Huỷ
            </Button>
            <Button
              variant="primary"
              onClick={onClose}
              className="flex-1 sm:flex-2"
            >
              Tiếp tục đăng ký
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationModal;
