// frontend/app/khoa-hoc/[slug]/layout.js
import strapi from "../../api/strapi"; // Đảm bảo đường dẫn này chính xác

// Hàm fetch dữ liệu khóa học
async function getCourseData(slug) {
  try {
    const course = await strapi.courses.getCourseBySlug(slug); // API call thực tế
     if (course && course.title && course.description) { // Kiểm tra các trường cần thiết
        return {
            title: course.title,
            description: course.description,
            slug: course.slug, // Cần slug để tạo canonical URL
            updatedAt: course.updatedAt || new Date().toISOString(), // Hoặc trường ngày phù hợp
            // featuredImage: course.featuredImage?.[0]?.url // Nếu có và muốn dùng cho OG image
        };
    }
    console.warn(`Course data not found or incomplete for slug: ${slug}`);
    return null;
  } catch (error) {
    console.error(`Error fetching course data for slug ${slug}:`, error);
    return null;
  }
}

export async function generateMetadata({ params: paramsPromise }) {
  // paramsPromise là một Promise, chúng ta cần await nó
  const params = await paramsPromise;
  const slugFromParams = params.slug;
  console.log(`Generating metadata for course slug: ${slugFromParams}`);

  // Phục hồi lại logic gọi getCourseData
  const course = await getCourseData(slugFromParams);

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  const defaultOgImage = `${siteUrl}/images/metadata-img/course-og.jpg`;

  if (!course) { // Bỏ comment
    return { // Trả về metadata tĩnh để thử nghiệm
      title: 'Không Tìm Thấy Khóa Học',
      description: 'Khóa học bạn tìm kiếm không tồn tại hoặc đã bị xóa.',
      alternates: {
        canonical: `${siteUrl}/khoa-hoc`,
      },
      openGraph: {
        title: 'Không Tìm Thấy Khóa Học',
        description: 'Khóa học bạn tìm kiếm không tồn tại hoặc đã bị xóa.',
        images: [{ url: defaultOgImage }],
        url: `${siteUrl}/khoa-hoc`,
        siteName: 'Ông Ba Dạy Hóa',
        type: 'article',
      },
      twitter: {
        card: 'summary_large_image',
        title: 'Không Tìm Thấy Khóa Học',
        description: 'Khóa học bạn tìm kiếm không tồn tại hoặc đã bị xóa.',
        images: [defaultOgImage],
      },
    };
  }

  // Phần code dưới này được sử dụng lại
  const courseOgImage = defaultOgImage; // Tạm thời vẫn dùng default, bạn có thể thay đổi logic này sau

  return {
    title: `${course.title} | Ông Ba Dạy Hóa`,
    description: course.description.substring(0, 160),
    alternates: {
      canonical: `${siteUrl}/khoa-hoc/${course.slug}`,
    },
    openGraph: {
      title: course.title,
      description: course.description.substring(0, 160),
      url: `${siteUrl}/khoa-hoc/${course.slug}`,
      siteName: 'Ông Ba Dạy Hóa',
      images: [
        {
          url: courseOgImage,
          width: 1200,
          height: 630,
          alt: course.title,
        },
      ],
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: course.title,
      description: course.description.substring(0, 160),
      images: [courseOgImage],
    },
  };
}

export default function CourseLayout({ children }) {
  return <>{children}</>;
}