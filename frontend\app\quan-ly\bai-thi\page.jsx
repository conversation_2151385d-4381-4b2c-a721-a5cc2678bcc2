"use client";

import DashboardLayout from "../../../components/layouts/DashboardLayout";
import CountdownWidget from "../../../components/dashboard/CountdownWidget";
import KaTeXRenderer from "@/components/katex-renderer";
import React, {useEffect} from "react";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";

const targetDate = new Date("2025-07-01");
const chemistryQuestions = [
    {
        id: 1,
        lesson: "Cân bằng phương trình hóa học",
        question: "Cân bằng phương trình hóa học sau: $\\ce{Al + HCl -> AlCl3 + H2}$",
        options: [
            "$\\ce{Al + 3HCl -> AlCl3 + 3H2}$",
            "$\\ce{2Al + 6HCl -> 2AlCl3 + 3H2}$",
            "$\\ce{Al + 2HCl -> AlCl3 + H2}$",
            "$\\ce{3Al + 9HCl -> 3AlCl3 + 3H2}$",
        ],
        correctAnswer: 1,
        explanation:
            "<PERSON><PERSON> cân bằng phương trình:\n\n1. <PERSON><PERSON><PERSON> số nguyên tử mỗi nguyên tố ở hai vế\n2. Al: 2 bên trái, 2 bên phải ✓\n3. H: 6 bên trái, 6 bên phải ✓\n4. Cl: 6 bên trái, 6 bên phải ✓\n\nPhương trình cân bằng: $\\ce{2Al + 6HCl -> 2AlCl3 + 3H2}$",
        lessonContent:
            "**Cân bằng phương trình hóa học** là quá trình điều chỉnh hệ số của các chất trong phương trình sao cho số nguyên tử của mỗi nguyên tố ở hai vế bằng nhau.\n\n**Các bước cân bằng:**\n1. Viết sơ đồ phản ứng\n2. Đặt hệ số thích hợp\n3. Kiểm tra số nguyên tử mỗi nguyên tố\n4. Điều chỉnh cho đến khi cân bằng",
    },
    {
        id: 2,
        lesson: "Tính toán theo phương trình hóa học",
        question:
            "Cho phản ứng: $\\ce{2H2 + O2 -> 2H2O}$. Nếu có 4 mol $\\ce{H2}$ phản ứng hoàn toàn, khối lượng $\\ce{H2O}$ tạo thành là bao nhiêu? (Biết $M_{\\ce{H2O}} = 18$ g/mol)",
        options: ["36 g", "72 g", "54 g", "18 g"],
        correctAnswer: 1,
        explanation:
            "**Giải:**\n\nTừ phương trình: $\\ce{2H2 + O2 -> 2H2O}$\n\nTỉ lệ mol: $n_{\\ce{H2}} : n_{\\ce{H2O}} = 2 : 2 = 1 : 1$\n\nVới $n_{\\ce{H2}} = 4$ mol\n\n$\\Rightarrow n_{\\ce{H2O}} = 4$ mol\n\n$m_{\\ce{H2O}} = n \\times M = 4 \\times 18 = 72$ g",
        lessonContent:
            "**Tính toán theo phương trình hóa học:**\n\n1. **Viết phương trình cân bằng**\n2. **Xác định tỉ lệ mol** từ hệ số trong phương trình\n3. **Tính số mol** chất cần tìm\n4. **Tính khối lượng** bằng công thức: $m = n \\times M$\n\n**Công thức quan trọng:**\n- $n = \\frac{m}{M}$ (mol)\n- $n = \\frac{V}{22.4}$ (ở đktc)\n- $C_M = \\frac{n}{V}$ (mol/L)",
    },
    {
        id: 3,
        lesson: "Axit - Bazơ - Muối",
        question: "Phản ứng nào sau đây tạo ra muối axit?",
        options: [
            "$\\ce{NaOH + HCl -> NaCl + H2O}$",
            "$\\ce{Ca(OH)2 + H2SO4 -> CaSO4 + 2H2O}$",
            "$\\ce{NaOH + H2SO4 -> NaHSO4 + H2O}$",
            "$\\ce{2NaOH + H2SO4 -> Na2SO4 + 2H2O}$",
        ],
        correctAnswer: 2,
        explanation:
            "**Muối axit** là muối còn chứa nguyên tử H có thể phân ly ra ion $\\ce{H+}$.\n\nTrong phản ứng: $\\ce{NaOH + H2SO4 -> NaHSO4 + H2O}$\n\n$\\ce{NaHSO4}$ là **natri hidrosunfat** - một muối axit vì:\n- Còn chứa H có thể phân ly: $\\ce{NaHSO4 -> Na+ + HSO4-}$\n- Ion $\\ce{HSO4-}$ có thể tiếp tục phân ly: $\\ce{HSO4- ⇌ H+ + SO4^2-}$",
        lessonContent:
            "**Phân loại muối:**\n\n1. **Muối trung hòa:** Không chứa H hoặc OH\n   - Ví dụ: $\\ce{NaCl, CaSO4, K2CO3}$\n\n2. **Muối axit:** Còn chứa H có thể phân ly\n   - Ví dụ: $\\ce{NaHCO3, Ca(HSO4)2, KH2PO4}$\n\n3. **Muối bazơ:** Còn chứa nhóm OH\n   - Ví dụ: $\\ce{Mg(OH)Cl, Al(OH)2Cl}$\n\n4. **Muối kép:** Chứa 2 cation hoặc 2 anion khác nhau\n   - Ví dụ: $\\ce{KAl(SO4)2, CaClBr}$",
    },
    {
        id: 4,
        lesson: "Oxi hóa - Khử",
        question: "Trong phản ứng: $\\ce{2Al + 3CuSO4 -> Al2(SO4)3 + 3Cu}$, chất khử là:",
        options: ["$\\ce{CuSO4}$", "$\\ce{Al}$", "$\\ce{Cu}$", "$\\ce{Al2(SO4)3}$"],
        correctAnswer: 1,
        explanation:
            "**Phân tích số oxi hóa:**\n\n$\\ce{Al^0 + Cu^{+2}SO4 -> Al^{+3}_2(SO4)3 + Cu^0}$\n\n- **Al:** $0 \\rightarrow +3$ (tăng 3 đơn vị) → bị oxi hóa → là **chất khử**\n- **Cu:** $+2 \\rightarrow 0$ (giảm 2 đơn vị) → bị khử → là **chất oxi hóa**\n\n**Quy tắc:**\n- Chất khử: cho electron, số oxi hóa tăng\n- Chất oxi hóa: nhận electron, số oxi hóa giảm",
        lessonContent:
            "**Phản ứng oxi hóa - khử:**\n\n**Khái niệm:**\n- **Oxi hóa:** Quá trình mất electron, số oxi hóa tăng\n- **Khử:** Quá trình nhận electron, số oxi hóa giảm\n\n**Quy tắc xác định số oxi hóa:**\n1. Nguyên tố tự do: số oxi hóa = 0\n2. Ion đơn nguyên tử: số oxi hóa = điện tích ion\n3. H thường có số oxi hóa +1\n4. O thường có số oxi hóa -2\n5. Tổng số oxi hóa trong hợp chất = 0\n\n**Cân bằng phương trình oxi hóa - khử:**\n1. Xác định số oxi hóa\n2. Viết quá trình oxi hóa và khử\n3. Cân bằng electron\n4. Cân bằng phương trình",
    },
    {
        id: 5,
        lesson: "Dung dịch - Nồng độ",
        question:
            "Tính nồng độ mol của dung dịch chứa 20g $\\ce{NaOH}$ trong 500ml dung dịch. Biết $M_{\\ce{NaOH}} = 40$ g/mol.",
        options: ["0.5 M", "1.0 M", "1.5 M", "2.0 M"],
        correctAnswer: 1,
        explanation:
            "**Giải:**\n\n**Cho:** $m_{\\ce{NaOH}} = 20$ g, $V_{dd} = 500$ ml $= 0.5$ L, $M_{\\ce{NaOH}} = 40$ g/mol\n\n**Tính số mol NaOH:**\n$n_{\\ce{NaOH}} = \\frac{m}{M} = \\frac{20}{40} = 0.5$ mol\n\n**Tính nồng độ mol:**\n$C_M = \\frac{n}{V} = \\frac{0.5}{0.5} = 1.0$ M\n\n**Đáp án:** 1.0 M",
        lessonContent:
            "**Các loại nồng độ dung dịch:**\n\n1. **Nồng độ phần trăm khối lượng:**\n   $C\\% = \\frac{m_{ct}}{m_{dd}} \\times 100\\%$\n\n2. **Nồng độ mol (molarity):**\n   $C_M = \\frac{n}{V}$ (mol/L)\n\n3. **Nồng độ molal:**\n   $C_m = \\frac{n}{m_{dm}}$ (mol/kg dung môi)\n\n4. **Nồng độ đương lượng:**\n   $C_N = \\frac{n_{đl}}{V}$ (N)\n\n**Công thức liên quan:**\n- $n = \\frac{m}{M}$ (số mol)\n- $m_{dd} = m_{ct} + m_{dm}$ (khối lượng dung dịch)\n- $V = \\frac{m_{dd}}{D}$ (thể tích dung dịch)",
    },
]

export default function BaiThi() {
    const question = chemistryQuestions[4];
    const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

    useEffect(() => {
        setTitle("Bài thi");
        setIsSearch(false);
        setIsDetail(false);
        setIsTurnLive(false);
        setKeySearch(keySearch);
        return () => {
            setIsSearch(false);
            setIsTurnLive(false);
        }
    }, []);

    return (
        // <DashboardLayout title="Bài thi">
        //
        // </DashboardLayout>
        // <div>
        //     <KaTeXRenderer content={question.explanation}/>
        //     {
        //         question.options.map((e, index) => {
        //             return <KaTeXRenderer key={index} content={e}/>
        //         })
        //     }
        // </div>

    <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-160px)]">
        <div className="bg-[#FFFFFF]  p-8 max-w-md w-full flex flex-col justify-center items-center">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="81"
                height="80"
                viewBox="0 0 81 80"
                fill="none"
            >
                <path
                    d="M64.9831 9.95187H62.7558L40.5016 13.1712L18.2475 9.95187H15.6986C12.9504 9.95187 10.7227 7.7241 10.7227 4.97594C10.7225 2.22777 12.9504 0 15.6986 0H64.9831C67.7313 0 69.959 2.22777 69.959 4.97594C69.9592 7.7241 67.7313 9.95187 64.9831 9.95187Z"
                    fill="#B8B9BD"
                />
                <path
                    d="M65.305 0H60.4761C63.2242 0 65.452 2.22777 65.452 4.97594C65.452 7.7241 63.2242 9.95187 60.4761 9.95187H65.305C68.0532 9.95187 70.281 7.7241 70.281 4.97594C70.2811 2.22777 68.0532 0 65.305 0Z"
                    fill="#A4A7AE"
                />
                <path
                    d="M62.434 16.1985V9.95187H18.2476V16.1985C18.2476 25.8886 24.4413 34.131 33.0856 37.1855C34.2787 37.6071 35.0765 38.7346 35.0765 39.9998C35.0765 41.2652 34.2787 42.3928 33.0856 42.8142C24.4414 45.8687 18.2476 54.1111 18.2476 63.8012V70.0478H62.4339V63.8012C62.4339 54.1111 56.2402 45.8687 47.5959 42.8142C46.4028 42.3926 45.6051 41.265 45.6051 39.9998H45.927C45.927 38.7345 46.4028 37.6071 47.5959 37.1855C56.2402 34.1312 62.434 25.8886 62.434 16.1985Z"
                    fill="#F5F5F5"
                />
                <path
                    d="M18.2476 9.95187V13.1066H54.8352C56.5427 13.1066 57.927 14.4909 57.927 16.1985C57.927 25.8585 51.7717 34.0798 43.1695 37.1569C42.1052 37.5375 41.291 38.4462 41.1296 39.5649C40.9224 41.0004 41.7607 42.3448 43.089 42.8142C51.7332 45.8687 57.927 54.1112 57.927 63.8012V70.0478H62.756V63.8012C62.756 54.1412 56.6006 45.9198 47.9985 42.8428C46.9342 42.4621 46.12 41.5535 45.9585 40.4348C45.7514 38.9993 46.5897 37.6549 47.918 37.1855C56.5622 34.131 62.756 25.8885 62.756 16.1985V9.95187H18.2476Z"
                    fill="#CED2D9"
                />
                <path
                    d="M64.9831 80H15.6986C12.9504 80 10.7227 77.7722 10.7227 75.0241C10.7227 72.2759 12.9504 70.0481 15.6986 70.0481H18.2478L40.502 66.8288L62.7561 70.0481H64.9834C67.7316 70.0481 69.9594 72.2759 69.9594 75.0241C69.9592 77.7722 67.7313 80 64.9831 80Z"
                    fill="#B8B9BD"
                />
                <path
                    d="M65.305 70.0481H60.4761C63.2242 70.0481 65.452 72.2759 65.452 75.0241C65.452 77.7722 63.2242 80 60.4761 80H65.305C68.0532 80 70.281 77.7722 70.281 75.0241C70.2811 72.2759 68.0532 70.0481 65.305 70.0481Z"
                    fill="#A4A7AE"
                />
                <path
                    d="M51.2181 52.5903H48.8743C46.5173 52.5903 44.1844 50.8851 43.7989 48.56L42.8426 42.7921C42.3558 39.8564 44.6203 37.1857 47.5961 37.1857C53.3707 35.1453 58.0482 30.7876 60.5198 25.2319H20.1619C22.6333 30.7878 27.311 35.1453 33.0856 37.1857C36.0614 37.1857 38.3259 39.8564 37.8391 42.7921L36.8828 48.56C36.4973 50.8851 34.4862 52.5903 32.1293 52.5903H29.4636C23.3189 52.5903 18.2813 57.5343 18.2481 63.6789C18.248 63.7198 18.2478 63.7606 18.2478 63.8015V70.0481L62.434 70.0481V63.8015C62.434 63.7606 62.4339 63.7198 62.4337 63.6789C62.4004 57.5343 57.3628 52.5903 51.2181 52.5903Z"
                    fill="#E9EAEB"
                />
                <path
                    d="M62.7557 63.6789C62.7223 57.5343 57.6849 52.5903 51.5402 52.5903H48.8743C46.5173 52.5903 44.5063 50.8851 44.1208 48.56L43.1645 42.7921C42.6777 39.8564 44.9422 37.1857 47.918 37.1857C53.6926 35.1453 58.3701 30.7876 60.8418 25.2319H56.0128C53.5413 30.7878 48.8637 35.1453 43.089 37.1857C40.1132 37.1857 37.8488 39.8564 38.3355 42.7921L39.2918 48.56C39.6773 50.8851 41.6885 52.5903 44.0453 52.5903H46.7112C52.8559 52.5903 57.8935 57.5343 57.9267 63.6789C57.9268 63.7198 57.927 63.7606 57.927 63.8015V70.0481H62.756V63.8015C62.756 63.7606 62.7558 63.7198 62.7557 63.6789Z"
                    fill="#DCDEE2"
                />
            </svg>
            <h3 className="text-lg text-[#414651] font-semibold text-center mt-2xl">
                Tính năng đang được phát triển
            </h3>
            {/*<CountdownWidget*/}
            {/*    targetDate={targetDate}*/}
            {/*    title="Thời gian khai giảng đếm ngược"*/}
            {/*    message="Trong lúc đợi... sao không cày view TikTok cho Ông Ba Dạy Hoá nhỉ? 🤗"*/}
            {/*    onComplete={() => console.log("Đếm ngược hoàn thành!")}*/}
            {/*/>*/}
        </div>
    </div>
    );
}
