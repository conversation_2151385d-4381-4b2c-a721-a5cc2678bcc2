// ExamIcon.jsx
import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const ExamIcon = ({width = 20, height = 20, isActive = false}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    const clipId = `clip-${Math.random().toString(36).substring(2, 9)}`
    if (!isActive) {
        return (
            <svg xmlns="http://www.w3.org/2000/svg"
                 width={width}
                 height={height}
                 viewBox={viewBox}
                 className="transition-colors duration-200"
            >
                <g clipPath="url(#clip0_3873_36890)">
                    <path strokeWidth={strokeWidth} d="M15.8316 9.1665C15.8316 9.62766 16.2055 10.0015 16.6666 10.0015C17.1278 10.0015 17.5016 9.62766 17.5016 9.1665H15.8316ZM8.33331 19.1682C8.79447 19.1682 9.16831 18.7943 9.16831 18.3332C9.16831 17.872 8.79447 17.4982 8.33331 17.4982V19.1682ZM3.6058 16.9681L4.34979 16.5891L4.34979 16.5891L3.6058 16.9681ZM4.69834 18.0607L4.31925 18.8047H4.31925L4.69834 18.0607ZM15.3016 1.93899L15.6807 1.195V1.195L15.3016 1.93899ZM16.3942 3.03153L15.6502 3.41061L15.6502 3.41061L16.3942 3.03153ZM4.69834 1.93899L5.07742 2.68298H5.07742L4.69834 1.93899ZM3.6058 3.03153L4.34979 3.41061L3.6058 3.03153ZM11.6666 10.0015C12.1278 10.0015 12.5016 9.62766 12.5016 9.1665C12.5016 8.70535 12.1278 8.3315 11.6666 8.3315V10.0015ZM6.66665 8.3315C6.20549 8.3315 5.83165 8.70535 5.83165 9.1665C5.83165 9.62766 6.20549 10.0015 6.66665 10.0015V8.3315ZM8.33331 13.3348C8.79447 13.3348 9.16831 12.961 9.16831 12.4998C9.16831 12.0387 8.79447 11.6648 8.33331 11.6648V13.3348ZM6.66665 11.6648C6.20549 11.6648 5.83165 12.0387 5.83165 12.4998C5.83165 12.961 6.20549 13.3348 6.66665 13.3348V11.6648ZM13.3333 6.66817C13.7945 6.66817 14.1683 6.29433 14.1683 5.83317C14.1683 5.37201 13.7945 4.99817 13.3333 4.99817V6.66817ZM6.66665 4.99817C6.20549 4.99817 5.83165 5.37201 5.83165 5.83317C5.83165 6.29433 6.20549 6.66817 6.66665 6.66817V4.99817ZM15.0432 16.9967C15.4557 17.2029 15.9573 17.0357 16.1635 16.6233C16.3697 16.2108 16.2025 15.7092 15.7901 15.503L15.0432 16.9967ZM14.5833 15.8332H13.7483C13.7483 16.1494 13.927 16.4386 14.2099 16.58L14.5833 15.8332ZM15.4183 14.1665C15.4183 13.7053 15.0445 13.3315 14.5833 13.3315C14.1222 13.3315 13.7483 13.7053 13.7483 14.1665H15.4183ZM7.33331 1.6665V2.5015H12.6666V1.6665V0.831504H7.33331V1.6665ZM3.33331 14.3332H4.16831V5.6665H3.33331H2.49831V14.3332H3.33331ZM16.6666 5.6665H15.8316V9.1665H16.6666H17.5016V5.6665H16.6666ZM8.33331 18.3332V17.4982H7.33331V18.3332V19.1682H8.33331V18.3332ZM3.33331 14.3332H2.49831C2.49831 15.0195 2.49766 15.5844 2.53515 16.0432C2.57343 16.5117 2.65559 16.9425 2.86181 17.3472L3.6058 16.9681L4.34979 16.5891C4.28352 16.459 4.22944 16.2724 4.1996 15.9072C4.16896 15.5322 4.16831 15.047 4.16831 14.3332H3.33331ZM7.33331 18.3332V17.4982C6.61947 17.4982 6.1343 17.4975 5.75931 17.4669C5.39407 17.437 5.20748 17.383 5.07742 17.3167L4.69834 18.0607L4.31925 18.8047C4.72398 19.0109 5.1548 19.0931 5.62332 19.1313C6.08209 19.1688 6.64703 19.1682 7.33331 19.1682V18.3332ZM3.6058 16.9681L2.86181 17.3472C3.18154 17.9748 3.69173 18.4849 4.31925 18.8047L4.69834 18.0607L5.07742 17.3167C4.76413 17.1571 4.50942 16.9024 4.34979 16.5891L3.6058 16.9681ZM12.6666 1.6665V2.5015C13.3805 2.5015 13.8657 2.50215 14.2406 2.53279C14.6059 2.56263 14.7925 2.61671 14.9225 2.68298L15.3016 1.93899L15.6807 1.195C15.276 0.988782 14.8452 0.906617 14.3766 0.868338C13.9179 0.830855 13.3529 0.831504 12.6666 0.831504V1.6665ZM16.6666 5.6665H17.5016C17.5016 4.98022 17.5023 4.41528 17.4648 3.95651C17.4265 3.488 17.3444 3.05717 17.1382 2.65245L16.3942 3.03153L15.6502 3.41061C15.7164 3.54067 15.7705 3.72726 15.8004 4.0925C15.831 4.4675 15.8316 4.95266 15.8316 5.6665H16.6666ZM15.3016 1.93899L14.9225 2.68298C15.2358 2.84261 15.4905 3.09732 15.6502 3.41061L16.3942 3.03153L17.1382 2.65245C16.8184 2.02492 16.3082 1.51473 15.6807 1.195L15.3016 1.93899ZM7.33331 1.6665V0.831504C6.64703 0.831504 6.08209 0.830855 5.62332 0.868338C5.1548 0.906617 4.72398 0.988782 4.31925 1.195L4.69834 1.93899L5.07742 2.68298C5.20748 2.61671 5.39407 2.56263 5.75931 2.53279C6.1343 2.50215 6.61947 2.5015 7.33331 2.5015V1.6665ZM3.33331 5.6665H4.16831C4.16831 4.95266 4.16896 4.4675 4.1996 4.0925C4.22944 3.72726 4.28352 3.54067 4.34979 3.41061L3.6058 3.03153L2.86181 2.65245C2.65559 3.05717 2.57343 3.488 2.53515 3.95651C2.49766 4.41528 2.49831 4.98022 2.49831 5.6665H3.33331ZM4.69834 1.93899L4.31925 1.195C3.69173 1.51473 3.18154 2.02492 2.86181 2.65245L3.6058 3.03153L4.34979 3.41061C4.50942 3.09732 4.76413 2.84261 5.07742 2.68298L4.69834 1.93899ZM11.6666 9.1665V8.3315H6.66665V9.1665V10.0015H11.6666V9.1665ZM8.33331 12.4998V11.6648H6.66665V12.4998V13.3348H8.33331V12.4998ZM13.3333 5.83317V4.99817H6.66665V5.83317V6.66817H13.3333V5.83317ZM18.3333 15.4165H17.4983C17.4983 17.0264 16.1932 18.3315 14.5833 18.3315V19.1665V20.0015C17.1155 20.0015 19.1683 17.9487 19.1683 15.4165H18.3333ZM14.5833 19.1665V18.3315C12.9734 18.3315 11.6683 17.0264 11.6683 15.4165H10.8333H9.99831C9.99831 17.9487 12.0511 20.0015 14.5833 20.0015V19.1665ZM10.8333 15.4165H11.6683C11.6683 13.8066 12.9734 12.5015 14.5833 12.5015V11.6665V10.8315C12.0511 10.8315 9.99831 12.8843 9.99831 15.4165H10.8333ZM14.5833 11.6665V12.5015C16.1932 12.5015 17.4983 13.8066 17.4983 15.4165H18.3333H19.1683C19.1683 12.8843 17.1155 10.8315 14.5833 10.8315V11.6665ZM15.4166 16.2498L15.7901 15.503L14.9567 15.0863L14.5833 15.8332L14.2099 16.58L15.0432 16.9967L15.4166 16.2498ZM14.5833 15.8332H15.4183V14.1665H14.5833H13.7483V15.8332H14.5833Z"
                          fill="currentColor"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_3873_36890">
                        <rect width={width} height={height} fill="white"/>
                    </clipPath>
                </defs>
            </svg>
        );
    }else {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox={viewBox} fill="none">
                <path strokeWidth={strokeWidth} fillRule="evenodd" clipRule="evenodd" d="M14.5833 10.8333C17.1146 10.8333 19.1667 12.8853 19.1667 15.4166C19.1667 17.9479 17.1146 19.9999 14.5833 19.9999C12.052 19.9999 10 17.9479 10 15.4166C10 12.8853 12.052 10.8333 14.5833 10.8333ZM14.5833 13.3333C14.1231 13.3333 13.75 13.7064 13.75 14.1666V15.8333C13.75 16.1489 13.9283 16.4375 14.2106 16.5787L15.0439 16.9954L15.1221 17.0295C15.517 17.1793 15.9691 17.0086 16.1621 16.6226C16.3551 16.2367 16.2202 15.7729 15.8634 15.5468L15.7894 15.5045L15.4167 15.3181V14.1666C15.4167 13.7064 15.0436 13.3333 14.5833 13.3333Z" fill="#299D55"/>
                <path strokeWidth={strokeWidth} fillRule="evenodd" clipRule="evenodd" d="M12.6668 0.833254C13.3531 0.833253 13.918 0.832402 14.3766 0.869875C14.845 0.908148 15.2759 0.99015 15.6803 1.19621L15.9098 1.32479C16.4319 1.64503 16.8574 2.10419 17.137 2.65291L17.2087 2.80591C17.363 3.16569 17.4299 3.54675 17.4634 3.95663C17.5009 4.41527 17.5 4.98019 17.5 5.66642V9.88843C16.6292 9.42804 15.6369 9.16659 14.5833 9.16659C13.8159 9.16659 13.0808 9.30496 12.4015 9.55803C12.4639 9.44122 12.5 9.30824 12.5 9.16659C12.5 8.70635 12.1269 8.33325 11.6667 8.33325H6.66667C6.20643 8.33325 5.83334 8.70635 5.83333 9.16659C5.83333 9.62682 6.20643 9.99992 6.66667 9.99992H11.4673C10.5022 10.5563 9.69991 11.3621 9.14876 12.3306C9.0705 11.9516 8.73552 11.6666 8.33333 11.6666H6.66667C6.20643 11.6666 5.83334 12.0397 5.83333 12.4999C5.83333 12.9602 6.20643 13.3333 6.66667 13.3333H8.33333C8.47499 13.3333 8.60797 13.2971 8.72477 13.2348C8.47171 13.914 8.33334 14.6492 8.33333 15.4166C8.33333 16.8237 8.79863 18.122 9.58333 19.1666H7.33317C6.64693 19.1666 6.08202 19.1674 5.62337 19.13C5.2135 19.0965 4.83243 19.0296 4.47266 18.8752L4.31966 18.8036C3.77094 18.524 3.31178 18.0985 2.99154 17.5764L2.86296 17.3469C2.6569 16.9425 2.5749 16.5116 2.53662 16.0432C2.49915 15.5846 2.5 15.0197 2.5 14.3334V5.66642C2.5 4.98019 2.49915 4.41527 2.53662 3.95663C2.5749 3.48825 2.6569 3.05733 2.86296 2.65291L2.99154 2.42342C3.31178 1.90134 3.77094 1.47583 4.31966 1.19621L4.47266 1.12459C4.83243 0.970271 5.2135 0.903368 5.62337 0.869875C6.08202 0.832402 6.64693 0.833253 7.33317 0.833254H12.6668ZM6.66667 4.99992C6.20643 4.99992 5.83334 5.37302 5.83333 5.83325C5.83333 6.29349 6.20643 6.66659 6.66667 6.66659H13.3333C13.7936 6.66659 14.1667 6.29349 14.1667 5.83325C14.1667 5.37302 13.7936 4.99992 13.3333 4.99992H6.66667Z" fill="#299D55"/>
            </svg>
        )
    }

};

export default ExamIcon;
