"use client";

import React, { useState, useContext } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import logo from "../../public/images/Logo.png"; // Đ<PERSON><PERSON> bảo đường dẫn đúng
import strapi from "../api/strapi"; // Đảm bảo đường dẫn đúng
import TextField from "../../components/TextField";
import { UserContext } from "../../context/UserProvider";

const ForgotPassPage = () => {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const { sendOTP } = useContext(UserContext);
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await sendOTP(email);
      if (response.success) {
        router.push(`/xac-thuc?from=quen-mat-khau&email=${email}`);
      } else {
        setError(response.error || "Không thể gửi mã OTP. Vui lòng thử lại.");
      }
    } catch (err) {
      setError(err.message || "Không thể gửi mã OTP. Vui lòng thử lại.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-screen">
      <div className="w-full bg-white flex items-center justify-center">
        <div className="max-w-[360px] w-full max-[390px]:px-4">
          <div className="flex flex-col items-center justify-center">
            <Image
              src={logo}
              alt="Logo"
              className="h-12 w-auto"
              onClick={() => router.push("/")}
            />
            <div className="text-3xl font-semibold text-[#181D27] mt-6">
              Quên mật khẩu
            </div>
            <div className="text-base font-normal text-[#535862] mt-3">
              Vui lòng nhập email cần lấy lại mật khẩu
            </div>
          </div>
          <form className="mt-6" onSubmit={handleSubmit}>
            {error && (
              <div className="mb-6 bg-[#FEF3F2] text-sm text-[#414651] font-normal flex gap-4 p-4 rounded-xl">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <g clipPath="url(#clip0_3814_10675)">
                    <path
                      d="M10.0001 6.66666V9.99999M10.0001 13.3333H10.0084M18.3334 9.99999C18.3334 14.6024 14.6025 18.3333 10.0001 18.3333C5.39771 18.3333 1.66675 14.6024 1.66675 9.99999C1.66675 5.39762 5.39771 1.66666 10.0001 1.66666C14.6025 1.66666 18.3334 5.39762 18.3334 9.99999Z"
                      stroke="#D92D20"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_3814_10675">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                {error}
              </div>
            )}
            <div>
              <TextField
                id="email"
                name="email"
                type="email"
                label="Email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Nhập Email của bạn"
                error={!!error}
              />
            </div>
            <div className="mt-6">
              <button
                type="submit"
                disabled={loading || !email}
                className={`w-full flex justify-center py-[10px] px-4 rounded-lg text-base font-semibold ${
                  loading || !email
                    ? "bg-[#F5F5F5] text-[#A4A7AE] cursor-not-allowed"
                    : "bg-[#299D55] text-[#FFF] hover:bg-[#198C43]"
                }`}
              >
                {loading ? "Đang gửi..." : "Gửi mã OTP"}
              </button>
            </div>
          </form>
          <div className="mt-8 text-center">
            <Link
              href="/dang-nhap"
              className="text-sm font-normal text-[#535862]"
            >
              ← Quay lại đăng nhập
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassPage;
