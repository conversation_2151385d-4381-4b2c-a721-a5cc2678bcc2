module.exports = ({ env }) => ({
  email: {
    config: {
      provider: 'strapi-provider-email-brevo',
      providerOptions: {
        apiKey: env('SENDINBLUE_API_KEY'),
      },
      settings: {
        defaultFrom: '<EMAIL>',
        defaultReplyTo: '<EMAIL>',
      },
    },
  },
  'users-permissions': {
    config: {
     
      providers: {
        google: {
          enabled: true,
          icon: 'google',
          key: env('GOOGLE_CLIENT_ID'),
          secret: env('GOOGLE_CLIENT_SECRET'),
          callback: '/api/auth/google/callback',
          scope: ['email', 'profile']
        },

      },
    },
  },
});