"use client";

import React, {useEffect, useRef, useState} from "react";
import clsx from "clsx";
import LineDivider from "@/components/icons/LineDivider";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {CommonUtil} from "@/utils/CommonUtil";
import {useScreenSize} from "@/hooks/useScreenSize";

/**
 * Popup hóa đơn mua khóa học
 * @param isOpen
 * @param onClose
 * @constructor
 */
const InvoiceDetailsModal = ({isOpen, onClose}) => {
    if (!isOpen) return;
    const [obj, setObj] = useState(true);
    const screenSize = useScreenSize();

    useEffect(() => {
        if (!isOpen) return;
        const handleKeyDown = (e) => {
            if (e.key === "Escape") onClose();
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [isOpen, onClose]);

    useEffect(() => {
        const cookies = new Cookies();
        const user_data = cookies.get('user_data');
        const getInfoInvoice = async () => {
            await strapi.users.getUserById(user_data.id).then(res => {
                if (res.data) {
                    const userData = res.data;
                    const infoInvoice = {};
                    infoInvoice.userName = userData.username;
                    infoInvoice.phone = userData.phone;
                    infoInvoice.email = userData.email;


                    const order = userData.orders[0];
                    const activation_codes = order.activation_codes[0];
                    const course_tier = order.course_tier;

                    infoInvoice.activeCode = activation_codes?.code;
                    infoInvoice.orderTime = order?.order_date ?  new Date(order.order_date).toLocaleString("vi-VN", {  hour: "2-digit",  minute: "2-digit",  day: "2-digit", month: "2-digit", year: "numeric", }) : '';
                    infoInvoice.address = order?.delivery_address;
                    infoInvoice.orderCode = order?.orderCode;
                    infoInvoice.courseTotal = course_tier?.price ? course_tier?.price.toLocaleString("vi-VN") : 0;
                    infoInvoice.courseVoucher = order?.discount_amount ? order?.discount_amount.toLocaleString("vi-VN") : 0;
                    infoInvoice.courseAmount = order?.discount_amount ? (course_tier?.price - order?.discount_amount).toLocaleString("vi-VN") : course_tier?.price.toLocaleString("vi-VN");
                    infoInvoice.isActive = order?.payment_status === 'completed';
                    infoInvoice.orderPayment = order?.payment_method ? order?.payment_method : 'Quét mã QR';

                    const course = order?.course;
                    debugger
                    infoInvoice.courseName = course.title;
                    infoInvoice.link_active = course.link_active;
                    infoInvoice.link_unactive = course.link_unactive;

                    setObj(infoInvoice);
                }else {

                }
            });
        }
        getInfoInvoice();
    },[]);
    const clickDiscord = () => {
        const link = obj.isActive ? obj.link_active : obj.link_unactive;
        if (link) {
            window.open(link, "_blank");
        }
    }
    return (
        <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
             style={{zIndex: 60}}
            tabIndex={-1}
            aria-modal="true"
            role="dialog">
            <div className="bg-[#FFFFFF] rounded-2xl shadow-xl w-full max-w-[514px] relative max-h-screen overflow-y-auto">
                <div className="header_model px-3xl pt-3xl mb-[20px]">
                    <div className="w-full h-full relative text-center">
                        <p className="header_title text-primary-900 text-xl leading-xl font-semibold">Hóa đơn</p>
                        <div className="button_close absolute right-0 top-1/2 -translate-y-1/2 cursor-pointer" onClick={onClose}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <LineDivider></LineDivider>
                <div className="content_model py-4xl px-3xl text-primary-900">
                    <div className="flex flex-col gap-[32px] ">
                        <div className="ma_kich_hoat flex flex-col gap-y-sm">
                            <div className="ma_kich_hoat text-left">
                                <p className="font-semibold text-md leading-md text-quaternary-500">Mã kích hoạt</p>
                            </div>
                            <div className={clsx(
                                "code_kich_hoat flex items-center",
                                !screenSize?.bw375640 ? 'flex-row justify-between' : 'flex-col gap-y-lg'
                            )}
                            >
                                <div className={clsx(
                                    "gap-[8px] flex flex-row",
                                    !screenSize?.bw375640 ? '' : 'w-full'
                                )}>
                                    <p className="text-primary-900 font-semibold text-md leading-md">{obj.activeCode}</p>
                                    <div className={clsx(
                                        "is_kich_hoat p-sm gap-x-xs rounded-sm flex flex-row border items-center h-[22px]",
                                        obj.isActive ? 'border-utility-success-200 bg-utility-success-50' : 'border-utility-warning-200 bg-utility-warning-50'
                                    )}>
                                        <div>
                                            {
                                                obj.isActive ?
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                                         viewBox="0 0 12 12" fill="none">
                                                        <path fillRule="evenodd" clipRule="evenodd"
                                                              d="M11 6C11 8.7615 8.7615 11 6 11C3.2385 11 1 8.7615 1 6C1 3.2385 3.2385 1 6 1C8.7615 1 11 3.2385 11 6ZM8.407 4.7905C8.44515 4.73705 8.4724 4.67661 8.48719 4.61263C8.50198 4.54866 8.50402 4.48239 8.49321 4.41762C8.48239 4.35285 8.45892 4.29085 8.42414 4.23515C8.38936 4.17945 8.34395 4.13115 8.2905 4.093C8.23705 4.05485 8.17661 4.0276 8.11263 4.01281C8.04866 3.99802 7.98239 3.99598 7.91762 4.00679C7.85285 4.01761 7.79085 4.04108 7.73515 4.07586C7.67945 4.11064 7.63115 4.15605 7.593 4.2095L5.436 7.2295L4.3535 6.1465C4.2592 6.05542 4.1329 6.00502 4.0018 6.00616C3.8707 6.0073 3.74529 6.05989 3.65259 6.15259C3.55989 6.24529 3.5073 6.3707 3.50616 6.5018C3.50502 6.6329 3.55542 6.7592 3.6465 6.8535L5.1465 8.3535C5.19785 8.40477 5.25974 8.44425 5.32788 8.4692C5.39602 8.49414 5.46877 8.50396 5.54109 8.49796C5.6134 8.49197 5.68354 8.47031 5.74665 8.43448C5.80975 8.39866 5.86429 8.34952 5.9065 8.2905L8.407 4.7905Z"
                                                              fill="#17B26A"/>
                                                    </svg>
                                                    :
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                        <path d="M8.5 1.67001C9.25413 2.10543 9.88147 2.73027 10.3199 3.48266C10.7583 4.23504 10.9927 5.08889 10.9997 5.95968C11.0067 6.83046 10.7862 7.68798 10.36 8.44736C9.93378 9.20673 9.31662 9.84161 8.56962 10.2892C7.82262 10.7367 6.97168 10.9814 6.10105 10.999C5.23042 11.0167 4.37028 10.8066 3.60578 10.3896C2.84128 9.97267 2.19894 9.36327 1.74233 8.62177C1.28573 7.88026 1.0307 7.03236 1.0025 6.16201L1 6.00001L1.0025 5.83801C1.0305 4.9745 1.28177 4.13299 1.73182 3.3955C2.18187 2.65802 2.81533 2.04973 3.57045 1.62995C4.32557 1.21016 5.17657 0.993206 6.0405 1.00023C6.90443 1.00725 7.7518 1.23801 8.5 1.67001ZM6 3.00001C5.87753 3.00002 5.75933 3.04499 5.66781 3.12636C5.5763 3.20774 5.51783 3.31988 5.5035 3.44151L5.5 3.50001V6.00001L5.5045 6.06551C5.5159 6.15226 5.54986 6.23449 5.603 6.30401L5.6465 6.35401L7.1465 7.85401L7.1935 7.89501C7.28119 7.96304 7.38902 7.99997 7.5 7.99997C7.61098 7.99997 7.71881 7.96304 7.8065 7.89501L7.8535 7.85351L7.895 7.80651C7.96303 7.71882 7.99996 7.61099 7.99996 7.50001C7.99996 7.38902 7.96303 7.28119 7.895 7.19351L7.8535 7.14651L6.5 5.79251V3.50001L6.4965 3.44151C6.48217 3.31988 6.4237 3.20774 6.33219 3.12636C6.24067 3.04499 6.12247 3.00002 6 3.00001Z" fill="#F79009"/>
                                                    </svg>
                                            }
                                        </div>
                                        <p className={clsx(
                                            "font-medium text-xs leading-xs",
                                            obj.isActive? 'text-utility-success-700' : 'text-utility-warning-700'
                                        )}>{obj.isActive? 'Đã kích hoạt': 'Chưa kích hoạt'}</p>
                                    </div>
                                </div>
                                <div className={clsx(
                                    "cursor-pointer  py-md px-lg h-[36px] gap-xs rounded-md border-[2px] border-skeuemorphic-gradient-border bg-[#5865F2] flex flex-row items-center justify-center",
                                    !screenSize?.bw375640 ? 'ml-[8px]' : 'w-full'
                                )}
                                     onClick={clickDiscord}
                                >
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16"
                                             fill="none">
                                            <path
                                                d="M16.7772 1.75221C15.514 1.17262 14.1595 0.745597 12.7432 0.501026C12.7174 0.496306 12.6916 0.508102 12.6784 0.531694C12.5042 0.841535 12.3112 1.24575 12.1761 1.56346C10.6528 1.33541 9.1373 1.33541 7.64525 1.56346C7.51009 1.23869 7.31012 0.841535 7.13513 0.531694C7.12184 0.508889 7.09608 0.497093 7.07029 0.501026C5.6548 0.744816 4.30025 1.17183 3.0363 1.75221C3.02536 1.75693 3.01598 1.7648 3.00976 1.77501C0.440459 5.61349 -0.26338 9.35762 0.0819 13.0553C0.0834623 13.0734 0.0936175 13.0907 0.107679 13.1017C1.80283 14.3466 3.44487 15.1024 5.05642 15.6033C5.08221 15.6112 5.10954 15.6017 5.12595 15.5805C5.50717 15.0599 5.84698 14.511 6.13834 13.9337C6.15554 13.8999 6.13912 13.8598 6.10398 13.8465C5.56497 13.642 5.05173 13.3927 4.55803 13.1096C4.51898 13.0868 4.51585 13.0309 4.55178 13.0042C4.65567 12.9263 4.75959 12.8453 4.85879 12.7636C4.87674 12.7486 4.90175 12.7455 4.92286 12.7549C8.16628 14.2357 11.6777 14.2357 14.8828 12.7549C14.9039 12.7447 14.9289 12.7478 14.9477 12.7628C15.0469 12.8446 15.1508 12.9263 15.2555 13.0042C15.2914 13.0309 15.289 13.0868 15.25 13.1096C14.7563 13.3982 14.243 13.642 13.7033 13.8457C13.6681 13.859 13.6525 13.8999 13.6697 13.9337C13.9673 14.5102 14.3071 15.0591 14.6813 15.5797C14.6969 15.6017 14.725 15.6112 14.7508 15.6033C16.3702 15.1024 18.0122 14.3466 19.7074 13.1017C19.7222 13.0907 19.7316 13.0742 19.7332 13.0561C20.1464 8.78115 19.041 5.06773 16.8029 1.7758C16.7975 1.7648 16.7881 1.75693 16.7772 1.75221ZM6.6227 10.8038C5.6462 10.8038 4.8416 9.90732 4.8416 8.80633C4.8416 7.70535 5.6306 6.80885 6.6227 6.80885C7.62258 6.80885 8.41939 7.71322 8.40376 8.80633C8.40376 9.90732 7.61476 10.8038 6.6227 10.8038ZM13.208 10.8038C12.2315 10.8038 11.4269 9.90732 11.4269 8.80633C11.4269 7.70535 12.2159 6.80885 13.208 6.80885C14.2079 6.80885 15.0047 7.71322 14.9891 8.80633C14.9891 9.90732 14.2079 10.8038 13.208 10.8038Z"
                                                fill="white"/>
                                        </svg>
                                    </div>
                                    <p className="text-sm leading-sm font-semibold text-[#FFFFFF]">{obj.isActive ? 'Vào lớp' : 'Tham gia Discord'}</p>
                                </div>
                            </div>
                        </div>
                        <div className="don_hang flex flex-col">
                            <div className="items-center gap-md flex flex-row">
                                <p className="text-primary-900 text-md leading-md font-semibold">Mã đơn hàng</p>
                                <p className="text-primary-900 text-md leading-md font-semibold">{obj.orderCode}</p>
                            </div>
                            <div className={clsx(
                                "flex ",
                                !screenSize?.bw375640 ? 'flex-row items-center gap-xs' : 'flex-col'
                            )}>
                                <p className="text-tertiary-600 text-md leading-md font-normal">Thanh toán lúc {obj.orderTime}</p>
                                {
                                    !screenSize?.bw375640 ?
                                    <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                                        <circle cx="2" cy="2" r="2" fill="#A4A7AE"/>
                                    </svg>
                                    :
                                    null
                                }

                                <p className="text-tertiary-600 text-md leading-md font-normal">{obj.orderPayment}</p>
                            </div>
                        </div>
                        <div className="thong_tin_ca_nhan flex flex-col">
                            <p className="text-primary-900 text-md leading-md font-semibold">Thông tin cá nhân</p>
                            <p className="text-tertiary-600 text-md leading-md font-normal">{obj.userName}</p>
                            <p className="text-tertiary-600 text-md leading-md font-normal">{obj.phone}</p>
                            <p className="text-tertiary-600 text-md leading-md font-normal">{obj.email}</p>
                        </div>
                        <div className="dia_chi flex flex-col">
                            <p className="text-primary-900 text-md leading-md font-semibold">Tài liệu giao tới</p>
                            <p className="text-tertiary-600 text-md leading-md font-normal">{obj.address}</p>
                        </div>
                        <div className="thanh_toan flex flex-col">
                            <LineDivider></LineDivider>
                            <div className="thong_tin_khoa py-xl flex flex-row justify-between">
                                <p className="text-primary-900 text-md leading-md font-semibold">{obj.courseName}</p>
                                <p className="text-primary-900 text-md leading-md font-semibold">{obj.courseTotal}đ</p>
                            </div>
                            <LineDivider></LineDivider>
                            <div className="thong_tin_tien py-xl flex flex-col">
                                <div className="flex flex-row justify-between">
                                    <p className="text-tertiary-600 text-md leading-md font-normal">Học phí</p>
                                    <p className="text-tertiary-600 text-md leading-md font-normal">{obj.courseTotal}đ</p>
                                </div>
                                <div className="flex flex-row justify-between">
                                    <p className="text-tertiary-600 text-md leading-md font-normal">Ưu đãi</p>
                                    <p className="text-tertiary-600 text-md leading-md font-normal">{obj.courseVoucher}đ</p>
                                </div>
                            </div>
                            <LineDivider></LineDivider>
                            <div className="tong_thanh_toan pt-xl flex flex-row justify-between">
                                <p className="text-primary-900 text-md leading-md font-semibold">Tổng thanh toán</p>
                                <p className="text-primary-900 text-md leading-md font-semibold">{obj.courseAmount}đ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InvoiceDetailsModal;
