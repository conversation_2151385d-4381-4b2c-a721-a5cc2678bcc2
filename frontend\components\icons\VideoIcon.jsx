import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const VideoIcon = ({width = 20, height = 20, isActive = false}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;

    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    if (!isActive) {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={width}
                height={height}
                viewBox={viewBox}
                fill="none"
                className="transition-colors duration-200"
            >
                <path
                    d="M7.91667 7.47111C7.91667 7.07337 7.91667 6.8745 7.99978 6.76348C8.07222 6.66673 8.18309 6.6062 8.30365 6.59759C8.44199 6.58771 8.60927 6.69524 8.94384 6.91032L12.8777 9.43921C13.168 9.62585 13.3131 9.71917 13.3633 9.83783C13.4071 9.9415 13.4071 10.0585 13.3633 10.1622C13.3131 10.2808 13.168 10.3742 12.8777 10.5608L8.94384 13.0897C8.60927 13.3048 8.44199 13.4123 8.30365 13.4024C8.18309 13.3938 8.07222 13.3333 7.99978 13.2365C7.91667 13.1255 7.91667 12.9266 7.91667 12.5289V7.47111Z"
                    stroke="currentColor"
                    strokeWidth={strokeWidth}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
                <path
                    d="M2.5 6.5C2.5 5.09987 2.5 4.3998 2.77248 3.86502C3.01217 3.39462 3.39462 3.01217 3.86502 2.77248C4.3998 2.5 5.09987 2.5 6.5 2.5H13.5C14.9001 2.5 15.6002 2.5 16.135 2.77248C16.6054 3.01217 16.9878 3.39462 17.2275 3.86502C17.5 4.3998 17.5 5.09987 17.5 6.5V13.5C17.5 14.9001 17.5 15.6002 17.2275 16.135C16.9878 16.6054 16.6054 16.9878 16.135 17.2275C15.6002 17.5 14.9001 17.5 13.5 17.5H6.5C5.09987 17.5 4.3998 17.5 3.86502 17.2275C3.39462 16.9878 3.01217 16.6054 2.77248 16.135C2.5 15.6002 2.5 14.9001 2.5 13.5V6.5Z"
                    stroke="currentColor"
                    strokeWidth={strokeWidth}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </svg>
        );
    }else {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox={viewBox} fill="#299D55">
                <path
                    d="M13.5001 1.66675C14.1864 1.66675 14.7513 1.6659 15.2099 1.70337C15.6783 1.74164 16.1092 1.82364 16.5136 2.02971L16.7431 2.15829C17.2652 2.47853 17.6907 2.93769 17.9703 3.48641L18.042 3.63941C18.1963 3.99918 18.2632 4.38025 18.2967 4.79012C18.3341 5.24877 18.3333 5.81368 18.3333 6.49992V13.5002C18.3333 14.1865 18.3341 14.7514 18.2967 15.21C18.2632 15.6199 18.1963 16.001 18.042 16.3608L17.9703 16.5138C17.6907 17.0625 17.2652 17.5216 16.7431 17.8419L16.5136 17.9705C16.1092 18.1765 15.6783 18.2585 15.2099 18.2968C14.7513 18.3343 14.1864 18.3334 13.5001 18.3334H6.4998C5.81356 18.3334 5.24865 18.3343 4.79 18.2968C4.38013 18.2633 3.99906 18.1964 3.63928 18.0421L3.48629 17.9705C2.93756 17.6908 2.47841 17.2653 2.15816 16.7432L2.02958 16.5138C1.82352 16.1093 1.74152 15.6784 1.70325 15.21C1.66578 14.7514 1.66663 14.1865 1.66663 13.5002V6.49992C1.66663 5.81368 1.66578 5.24877 1.70325 4.79012C1.74152 4.32174 1.82352 3.89083 2.02958 3.48641L2.15816 3.25692C2.47841 2.73483 2.93756 2.30933 3.48629 2.02971L3.63928 1.95809C3.99906 1.80377 4.38013 1.73686 4.79 1.70337C5.24865 1.6659 5.81356 1.66675 6.4998 1.66675H13.5001ZM7.88733 6.66756C7.76678 6.67617 7.6554 6.73683 7.58297 6.83358C7.50005 6.94464 7.49996 7.14342 7.49996 7.54077V12.5986C7.49996 12.9963 7.49985 13.1955 7.58297 13.3066C7.6554 13.4032 7.76682 13.464 7.88733 13.4726C8.02559 13.4822 8.19275 13.3741 8.52698 13.1593L12.4609 10.6308C12.7512 10.4441 12.8966 10.3507 12.9467 10.232C12.9905 10.1283 12.9905 10.011 12.9467 9.90731C12.8965 9.78876 12.751 9.69584 12.4609 9.50936L8.52698 6.98006C8.1928 6.76523 8.02558 6.6579 7.88733 6.66756Z"
                    fill="#299D55"
                />
            </svg>
        )
    }

};

export default VideoIcon;
