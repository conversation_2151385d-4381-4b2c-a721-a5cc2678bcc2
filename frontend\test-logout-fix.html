<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logout Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            margin: 8px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
            touch-action: manipulation;
        }
        .menu-item:hover {
            background-color: #f5f5f5;
        }
        .menu-item:active {
            background-color: #e5e5e5;
        }
        .menu-item.button-style {
            background: none;
            border: 1px solid #ddd;
            width: 100%;
            text-align: left;
        }
        .icon {
            margin-right: 12px;
            width: 24px;
            height: 24px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f0f8ff;
        }
    </style>
</head>
<body>
    <h1>Test Mobile Logout Fix</h1>
    <p>Test các cách implement menu items:</p>
    
    <h3>1. DIV với onClick (cũ):</h3>
    <div class="menu-item" onclick="handleClick('div')">
        <div class="icon">🔓</div>
        <span>Đăng xuất (DIV)</span>
    </div>
    
    <h3>2. BUTTON element (mới):</h3>
    <button class="menu-item button-style" onclick="handleClick('button')" type="button">
        <div class="icon">🔓</div>
        <span>Đăng xuất (BUTTON)</span>
    </button>
    
    <h3>3. BUTTON với proper mobile styling:</h3>
    <button 
        class="menu-item button-style" 
        onclick="handleClick('button-mobile')" 
        type="button"
        style="touch-action: manipulation; -webkit-tap-highlight-color: transparent;"
    >
        <div class="icon">🔓</div>
        <span>Đăng xuất (BUTTON + Mobile)</span>
    </button>
    
    <div class="result" id="result">
        Click vào các nút trên để test...
    </div>
    
    <script>
        function handleClick(type) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.innerHTML = `
                <strong>✅ ${type.toUpperCase()} clicked!</strong><br>
                Time: ${timestamp}<br>
                Type: ${type}<br>
                User Agent: ${navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}
            `;
            
            // Simulate logout action
            console.log(`Logout triggered via ${type} at ${timestamp}`);
        }
        
        // Test touch events
        document.addEventListener('touchstart', function(e) {
            console.log('Touch start detected on:', e.target.tagName);
        });
        
        document.addEventListener('touchend', function(e) {
            console.log('Touch end detected on:', e.target.tagName);
        });
    </script>
</body>
</html>
