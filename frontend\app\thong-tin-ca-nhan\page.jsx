"use client";

import React, { useState, useRef, useEffect, useContext } from "react";
import logo from "../../public/images/Logo.png";
import Image from "next/image";
import LocationSelector from "../../components/LocationSelector";
import Button from "../../components/Button";
import strapi from "../api/strapi";
import { useRouter } from "next/navigation";
import { useNotification } from "../../context/NotificationContext";
import TextField from "../../components/TextField";
import RadioGroup from "../../components/RadioGroup";
import { UserContext } from "../../context/UserProvider";

const UserFormPage = () => {
  const [errors, setErrors] = useState({});
  const [address, setAddress] = useState("");
  const [tempAddress, setTempAddress] = useState("");
  const [gender, setGender] = useState("");
  const { user, updateUser } = useContext(UserContext);
  const [formData, setFormData] = useState({
    fullname: "",
    date: "",
    phone: "",
  });

  const [showLocationSelector, setShowLocationSelector] = useState(false);
  const locationSelectorRef = useRef(null);
  const addressInputRef = useRef(null);
  const router = useRouter();

  // Get user from localStorage
  useEffect(() => {
    if (!user) {
      router.push("/dang-nhap");
    } else {
      // Nếu user đã có data, fill vào form
      if (user.fullname)
        setFormData((prev) => ({ ...prev, fullname: user.fullname }));
      if (user.date) setFormData((prev) => ({ ...prev, date: user.date }));
      if (user.phone) setFormData((prev) => ({ ...prev, phone: user.phone }));
      if (user.gender !== undefined) setGender(user.gender ? "Nam" : "Nữ");
    }
  }, [router, user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    let currentErrors = { ...errors };

    if (name === "fullname") {
      const words = value.trim().split(/\s+/);
      currentErrors.fullname =
        value === "" || words.length < 2
          ? "Họ và tên phải có ít nhất 2 từ"
          : null;
    }

    if (name === "date") {
      if (value === "") {
        currentErrors.date = "Ngày sinh không được để trống";
      } else {
        const selectedDate = new Date(value);
        const minDate = new Date("1900-01-01");
        const maxDate = new Date("2025-12-31");
        currentErrors.date =
          selectedDate >= minDate && selectedDate <= maxDate
            ? null
            : "Ngày sinh không hợp lệ";
      }
    }

    if (name === "phone") {
      const phoneRegex = /(84|0[3|5|7|8|9])+([0-9]{8})\b/;
      currentErrors.phone =
        value === "" || !phoneRegex.test(value)
          ? "Số điện thoại không hợp lệ (VD: 0912345678)"
          : null;
    }

    setErrors(currentErrors);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const selectedSources = [];
    if (e.target.facebook?.checked) selectedSources.push("Facebook");
    if (e.target.tiktok?.checked) selectedSources.push("Tiktok");
    if (e.target.friendRecommendation?.checked)
      selectedSources.push("Bạn bè giới thiệu");

    const submitData = {
      fullname: formData.fullname,
      date: formData.date,
      gender: gender === "Nam" ? true : false,
      phone: formData.phone,
      whereYouKnowWebsite: selectedSources.join(", "),
    };

    try {
      const result = await updateUser(submitData);

      if (result.success) {
        const courseDataString = localStorage.getItem("coursedata");
        if (courseDataString) {
          try {
            const courseData = JSON.parse(courseDataString);
            const courseParam = `${courseData.slug}-${courseData.tier_type}`;
            const url = `/thanh-toan?${courseParam}`;
            showNotification({
              type: "success",
              title: "Cập nhật thành công",
              message: "Thông tin của bạn đã được cập nhật!",
              duration: 3000,
            });
            router.push(url);
          } catch (err) {
            console.error("Lỗi khi đọc dữ liệu khóa học:", err);
            showNotification({
              type: "error",
              title: "Lỗi",
              message:
                "Không thể chuyển hướng đến trang thanh toán. Vui lòng thử lại sau.",
              duration: 5000,
            });
          }
        } else {
          showNotification({
            type: "success",
            title: "Cập nhật thành công",
            message: "Thông tin của bạn đã được cập nhật!",
            duration: 3000,
          });
          setTimeout(() => {
            router.push("/");
          }, 3000);
        }
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      showNotification({
        type: "error",
        title: "Lỗi",
        message:
          err.response?.data?.message ||
          err.message ||
          "Không thể cập nhật thông tin. Vui lòng thử lại sau.",
        duration: 5000,
      });
    }
  };

  const { showNotification } = useNotification();

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#FFFFFF]">
      <div className="w-full flex items-center justify-center">
        <div className="max-w-[489px] w-full flex flex-col items-center py-12 px-4">
          <div className="flex justify-center mb-3">
            <Image
              src="/images/Logo.png"
              alt="Logo"
              width={120}
              height={48}
              className="h-12 w-auto"
              onClick={() => router.push("/")}
            />
          </div>
          <div className="text-center">
            <h2 className="text-[30px] leading-[38px] font-semibold text-[#181D27]">
              Thông tin cá nhân
            </h2>
            <p className="mt-3 font-normal text-base text-[#535862]">
              Nhập thông tin của bạn để được hỗ trợ tốt hơn
            </p>
          </div>
          <form className="mt-8 w-full" onSubmit={handleSubmit}>
            <TextField
              label="Họ và Tên"
              name="fullname"
              type="text"
              value={formData.fullname}
              onChange={handleInputChange}
              placeholder="Nhập họ và tên"
              required
              error={errors.fullname}
            />

            <div className="w-full mt-[20px] flex flex-col min-[520px]:flex-row items-start gap-4">
              <TextField
                label="Ngày tháng năm sinh"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleInputChange}
                required
                error={errors.date}
                className="w-full sm:w-1/2"
              />
              <div className="w-full sm:w-1/2 mt-2 min-[520px]:mt-0">
                <RadioGroup
                  label="Giới tính"
                  required
                  options={[
                    { value: "Nam", label: "Nam" },
                    { value: "Nữ", label: "Nữ" },
                  ]}
                  value={gender}
                  onChange={setGender}
                  error={errors.gender}
                  helperText={errors.gender}
                />
              </div>
            </div>

            <div className="w-full mt-[20px]">
              <label className="block text-sm font-medium text-[#414651]">
                Bạn biết website này từ đâu?
              </label>
              <div className="mt-2 w-full flex items-center justify-start">
                <div className="flex items-center">
                  <input
                    id="facebook"
                    name="facebook"
                    type="checkbox"
                    className="h-4 w-4 border border-[#D5D7DA] rounded bg-white focus:outline-none checked:bg-[#299D55] checked:border-[#299D55] accent-[#299D55]"
                  />
                  <label
                    htmlFor="facebook"
                    className="ml-2 block text-sm font-medium text-[#414651]"
                  >
                    Facebook
                  </label>
                </div>
                <div className="flex items-center pl-6">
                  <input
                    id="tiktok"
                    name="tiktok"
                    type="checkbox"
                    className="h-4 w-4 border border-[#D5D7DA] rounded bg-white focus:outline-none checked:border-[#299D55] accent-[#299D55]"
                  />
                  <label
                    htmlFor="tiktok"
                    className="ml-2 block text-sm font-medium text-[#414651]"
                  >
                    Tiktok
                  </label>
                </div>
                <div className="flex items-center pl-6">
                  <input
                    id="friendRecommendation"
                    name="friendRecommendation"
                    type="checkbox"
                    className="h-4 w-4 border border-[#D5D7DA] rounded bg-white focus:outline-none checked:bg-[#299D55] checked:border-[#299D55] accent-[#299D55]"
                  />
                  <label
                    htmlFor="friendRecommendation"
                    className="ml-2 block text-sm font-medium text-[#414651]"
                  >
                    Bạn bè giới thiệu
                  </label>
                </div>
              </div>
            </div>
            <div className="w-full mt-[32px]">
              <Button
                variant="primary"
                // disabled={
                //   !formData.fullname ||
                //   !formData.date ||
                //   !formData.phone ||
                //   !formData.gender ||
                //   Object.keys(errors).length > 0
                // }
                className="w-full"
                type="submit"
              >
                Lưu thông tin
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserFormPage;
