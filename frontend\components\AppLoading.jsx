"use client";
import React, { useState, useEffect, useRef } from "react";

const AppLoading = ({ isLoading = false}) => {

  if (isLoading ) {
    return (
        <div className="fixed top-0 left-0 w-screen h-screen bg-[#f0f8ff47] z-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#DD2590]"></div>
        </div>
    );
  }
};

export default AppLoading;
