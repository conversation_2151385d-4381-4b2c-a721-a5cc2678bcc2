import { MetadataRoute } from 'next';
import strapi from "./api/strapi"; // <PERSON><PERSON><PERSON> bảo đường dẫn này chính xác

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

type ChangeFrequency = 'daily' | 'weekly' | 'monthly' | 'yearly' | 'always' | 'hourly' | 'never';

interface Slug {
  slug: string;
  lastModified: string;
}

async function getAllPostSlugs(): Promise<Slug[]> {
  try {
    // const response = await strapi.blog.getAllPosts({ fields: ['slug', 'updatedAt', 'publishedAt', 'createdAt'] });
    const response = await strapi.blog.getAllPosts();
    if (response && response.data) {
      return response.data.map(post => ({
        slug: post.slug,
        lastModified: post.updatedAt || post.publishedAt || post.createdAt || new Date().toISOString(),
      }));
    }
    return [];
  } catch (error) {
    console.error("Error fetching post slugs for sitemap:", error);
    return [];
  }
}

async function getAllCourseSlugs(): Promise<Slug[]> {
  try {
    // Lấy tất cả khóa học từ Strapi
    const response = await strapi.courses.getAllCourses();
    // Xác định mảng khóa học trả về
    let courses: any[] = [];
    if (response && Array.isArray(response.data)) {
      courses = response.data;
    } else if (response && response.data && Array.isArray(response.data.data)) {
      courses = response.data.data;
    }
    return courses.map(item => ({
      slug: item.attributes?.slug || item.slug,
      lastModified:
        item.attributes?.updatedAt ||
        item.attributes?.publishedAt ||
        item.attributes?.createdAt ||
        item.updatedAt ||
        item.publishedAt ||
        item.createdAt ||
        new Date().toISOString(),
    }));
  } catch (error) {
    console.error("Error fetching course slugs for sitemap:", error);
    return [];
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const staticPages: MetadataRoute.Sitemap = [
    { url: `${SITE_URL}/`, lastModified: new Date(), changeFrequency: 'daily' as ChangeFrequency, priority: 1.0 },
    { url: `${SITE_URL}/bai-viet`, lastModified: new Date(), changeFrequency: 'daily' as ChangeFrequency, priority: 0.8 },
  ];

  const postSlugs = await getAllPostSlugs();
  const postUrls = postSlugs.map(post => ({
    url: `${SITE_URL}/bai-viet/${post.slug}`,
    lastModified: new Date(post.lastModified),
    changeFrequency: 'weekly' as ChangeFrequency,
    priority: 0.7,
  }));

  const courseSlugs = await getAllCourseSlugs();
  const courseUrls = courseSlugs.map(course => ({
    url: `${SITE_URL}/khoa-hoc/${course.slug}`,
    lastModified: new Date(course.lastModified),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.9,
  }));

  return [
    ...staticPages,
    ...postUrls,
    ...courseUrls,
  ];
}

// Tùy chọn: Revalidate sitemap sau một khoảng thời gian nhất định (tính bằng giây)
// export const revalidate = 86400; // 1 ngày (24 * 60 * 60) 