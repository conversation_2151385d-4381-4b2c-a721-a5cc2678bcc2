import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const PracticeIcon = ({width = 20, height = 20,isActive = false}) => {
    width = Number(width) || 20;
    height = Number(height) || 20;

    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    let viewBox = `0 0 ${width} ${height}`;
    if (!isActive) {
        return (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width={width}
                height={height}
                viewBox={viewBox}
                fill="none"
                className="transition-colors duration-200"
            >
                <path
                    d="M4.59073 12.082V13.4706C4.58075 13.8453 4.9196 14.1563 5.34785 14.1654H6.86329C7.29154 14.1563 7.63039 13.8453 7.62041 13.4706L7.62041 6.52682C7.63039 6.15209 7.29154 5.84113 6.86329 5.83203H5.34785C4.9196 5.84113 4.58075 6.15209 4.59073 6.52682V7.91536M4.59073 12.082H2.42531C1.99706 12.0729 1.6582 11.762 1.66818 11.3872L1.66818 8.61016C1.6582 8.23542 1.99706 7.92446 2.42531 7.91536L4.59073 7.91536M4.59073 12.082L4.59073 7.91536M15.4131 7.91536L15.4131 12.082M7.62043 9.9987L12.3822 9.9987M14.6548 14.1654H13.1393C12.7111 14.1563 12.3722 13.8453 12.3822 13.4706L12.3822 6.52682C12.3722 6.15209 12.7111 5.84113 13.1393 5.83203L14.6548 5.83203C15.083 5.84113 15.4219 6.15209 15.4119 6.52682V7.91536H17.5761C17.782 7.91946 17.9776 7.99498 18.1198 8.12529C18.262 8.25559 18.3392 8.43002 18.3344 8.61016V11.3872C18.3444 11.762 18.0055 12.0729 17.5773 12.082H15.4119V13.4706C15.4219 13.8453 15.083 14.1563 14.6548 14.1654Z"
                    stroke="currentColor"
                    strokeWidth={strokeWidth}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </svg>
        );
    }else {
        return (
            <svg  width={width}
                  height={height}
                  viewBox={viewBox}
                  fill="none" xmlns="http://www.w3.org/2000/svg">
                <path strokeWidth={strokeWidth} d="M7.02641 5.00977C7.75374 5.09157 8.46241 5.66103 8.45137 6.52832L8.45219 9.16667H11.5479V6.52832C11.5361 5.60283 12.3449 5.01656 13.1202 5H14.6713L14.8177 5.00977C15.5451 5.09152 16.2538 5.66098 16.2427 6.52832H16.2443V7.08333H17.5912C17.9356 7.09019 18.2804 7.20145 18.5637 7.41455L18.6817 7.51221L18.7907 7.62288C19.0274 7.88832 19.1694 8.23512 19.1651 8.61165H19.1667V11.3883H19.1651C19.1761 12.2558 18.4675 12.8252 17.7401 12.9069L17.5936 12.9167H16.2443V13.4717H16.2427C16.2538 14.339 15.5451 14.9085 14.8177 14.9902L14.6713 15H13.1202C12.3449 14.9834 11.5369 14.3972 11.5487 13.4717L11.5479 10.8333H8.45219V13.4717C8.46323 14.339 7.75374 14.9084 7.02641 14.9902L6.87992 15H5.32882C4.55353 14.9835 3.74558 14.3972 3.75736 13.4717H3.75574V12.9167H2.40645C1.63114 12.9002 0.823218 12.3139 0.835002 11.3883H0.833374V8.61165H0.835002C0.823218 7.68608 1.63114 7.0998 2.40645 7.08333H3.75574V6.52832H3.75736C3.74558 5.60278 4.55353 5.0165 5.32882 5H6.87992L7.02641 5.00977Z" fill="#299D55"/>
            </svg>
        )
    }

};

export default PracticeIcon;
