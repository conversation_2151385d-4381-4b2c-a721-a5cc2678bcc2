import { MetadataRoute } from 'next';

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*', // Áp dụng cho tất cả các bot
        allow: '/',     // Cho phép crawl tất cả các trang theo mặc định
        // Disallow các trang không muốn index, ví dụ:
        disallow: [
          '/quan-ly/',          // Khu vực quản lý
          '/thanh-toan/',       // Trang thanh toán
          '/xac-thuc/',         // Trang xác thực OTP
          '/mat-khau-moi/',     // Trang đặt lại mật khẩu
          '/thong-tin-ca-nhan/',// Trang thông tin cá nhân (nếu không muốn public)
          '/tai-khoan/',        // Trang tài khoản (nếu không muốn public)
          '/hoa-don/',          // Trang hóa đơn
          '/api/',              // Tất cả các API routes
          // Cân nhắc thêm các trang khác nếu cần
        ],
      },
      // Bạn có thể thêm các quy tắc cụ thể cho các bot khác nếu cần
      // Ví dụ:
      // {
      //   userAgent: 'Googlebot-Image',
      //   allow: ['/images/'], // Chỉ cho phép Googlebot-Image crawl thư mục ảnh
      // },
    ],
    sitemap: `${SITE_URL}/sitemap.xml`,
  };
} 