"use client";

import React, { useRef, useEffect } from "react";

const VideoPlayer = ({ src, poster, autoplay = false, muted = false }) => {
  const videoRef = useRef(null);
  const playerRef = useRef(null);
  const hlsRef = useRef(null);

  useEffect(() => {
    const loadScripts = async () => {
      // Load Plyr CSS
      if (!document.querySelector('link[href*="plyr.css"]')) {
        const plyrCSS = document.createElement("link");
        plyrCSS.rel = "stylesheet";
        plyrCSS.href = "https://cdn.plyr.io/3.7.8/plyr.css";
        document.head.appendChild(plyrCSS);
      }

      // Load HLS.js
      if (!window.Hls) {
        await new Promise((resolve) => {
          const hlsScript = document.createElement("script");
          hlsScript.src = "https://cdn.jsdelivr.net/npm/hls.js@latest";
          hlsScript.onload = resolve;
          document.head.appendChild(hlsScript);
        });
      }

      // Load Plyr
      if (!window.Plyr) {
        await new Promise((resolve) => {
          const plyrScript = document.createElement("script");
          plyrScript.src = "https://cdn.plyr.io/3.7.8/plyr.polyfilled.js";
          plyrScript.onload = resolve;
          document.head.appendChild(plyrScript);
        });
      }

      initializePlayer();
    };

    loadScripts();
  }, [src]);

  const initializePlayer = () => {
    if (!videoRef.current || !src || !window.Hls || !window.Plyr) return;

    const video = videoRef.current;

    if (window.Hls.isSupported()) {
      const hls = new window.Hls();
      hlsRef.current = hls;
      hls.loadSource(src);
      hls.attachMedia(video);

      hls.on(window.Hls.Events.MANIFEST_PARSED, (_, data) => {
        initPlyr(data.levels);
      });

      hls.on(window.Hls.Events.ERROR, (_, data) => {});
    } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
      video.src = src;
      initPlyr([]);
    }
  };

  const initPlyr = (levels) => {
    if (!videoRef.current || !window.Plyr) return;

    // Build quality options theo format đúng
    const qualityOptions = levels.map((level) => ({
      src: "",
      type: "video/mp4",
      size: level.height,
    }));

    // Add Auto option
    qualityOptions.unshift({
      src: "",
      type: "video/mp4",
      size: "Auto",
    });

    const player = new window.Plyr(videoRef.current, {
      controls: [
        "play-large",
        "play",
        "progress",
        "current-time",
        "duration",
        "settings",
        "fullscreen",
      ],
      settings: ["quality", "speed"],
      quality: {
        default: "Auto",
        options: qualityOptions.map((q) => q.size),
        forced: true,
        onChange: (quality) => {
          if (hlsRef.current) {
            if (quality === "Auto") {
              hlsRef.current.currentLevel = -1;
            } else {
              const levelIndex = levels.findIndex(
                (level) => level.height.toString() === quality.toString()
              );
              if (levelIndex !== -1) {
                hlsRef.current.currentLevel = levelIndex;
              }
            }
          }
        },
      },
      speed: {
        selected: 1,
        options: [0.5, 0.75, 1, 1.25, 1.5, 2],
      },
      seekTime: 10,
      autoplay,
      muted,
      poster: poster,
      i18n: {
        restart: "Khởi động lại",
        rewind: "Tua lùi {seektime}s",
        play: "Phát",
        pause: "Tạm dừng",
        fastForward: "Tua tới {seektime}s",
        seek: "Tìm kiếm",
        seekLabel: "{currentTime} của {duration}",
        played: "Đã phát",
        buffered: "Đã tải",
        currentTime: "Thời gian hiện tại",
        duration: "Thời lượng",
        volume: "Âm lượng",
        mute: "Tắt tiếng",
        unmute: "Bật tiếng",
        enableCaptions: "Bật phụ đề",
        disableCaptions: "Tắt phụ đề",
        download: "Tải xuống",
        enterFullscreen: "Toàn màn hình",
        exitFullscreen: "Thoát toàn màn hình",
        frameTitle: "Trình phát cho {title}",
        captions: "Phụ đề",
        settings: "Cài đặt",
        pip: "PIP",
        menuBack: "Quay lại menu trước",
        speed: "Tốc độ",
        normal: "Bình thường",
        quality: "Chất lượng",
        loop: "Lặp lại",
        start: "Bắt đầu",
        end: "Kết thúc",
        all: "Tất cả",
        reset: "Đặt lại",
        disabled: "Đã tắt",
        enabled: "Đã bật",
        advertisement: "Quảng cáo",
        qualityBadge: {
          1080: "HD",
          720: "HD",
        },
      },
    });

    playerRef.current = player;
  };

  useEffect(() => {
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
      }
      if (playerRef.current) {
        playerRef.current.destroy();
      }
    };
  }, []);

  return (
    <div className="video-player">
      <video ref={videoRef} playsInline poster={poster} />

      <style jsx global>{`
        /* Import Inter font */
        @import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

        :root {
          --plyr-color-main: #00d4aa;
        }

        .plyr * {
          font-weight: 600 !important;
        }
      `}</style>
    </div>
  );
};

export default VideoPlayer;
