"use client";

// src/context/UserContext.js
import React, {createContext, useState, useEffect, useMemo} from "react";
import {useCookies} from "react-cookie";
import {useRouter} from "next/navigation";
import { useToast } from "./ToastContext";

export const UserContext = createContext();

export const UserProvider = ({children}) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [logoutLoading, setLogoutLoading] = useState(false);
    const [cookies, setCookie] = useCookies([
        "user_data",
        "token",
        "access_token",
        "hasCompletedOrder",
        "completedOrderInfo",
    ]);
    const router = useRouter();
    const [token, setToken] = useState(null);
    const [completedOrderInfo, setCompletedOrderInfo] = useState(null);
    const toast = useToast();

    const isAuthenticated = useMemo(() => {
        return !!cookies.user_data;
    }, [cookies.user_data]);

    useEffect(() => {
        // Khởi tạo state user từ cookie nếu có
        if (cookies.user_data) {
            try {
                const userData = cookies.user_data;

                // Lấy token từ access_token nếu có, hoặc từ response khi đăng nhập
                const currentToken = cookies.access_token;

                setUser(userData);
                setToken(currentToken);

                const orderInfo = cookies.completedOrderInfo;

                setCompletedOrderInfo(orderInfo);
            } catch (error) {
                console.error("Error parsing user data from cookie", error);
            }
        }

        setLoading(false);
    }, [
        cookies.user_data,
        cookies.access_token,
        cookies.hasCompletedOrder,
        cookies.completedOrderInfo,
    ]);

    // Tạo hàm kiểm tra trạng thái đơn hàng
    const checkOrderStatus = async () => {
        if (user && user.id) {
            try {
                const response = await fetch("/api/auth/check-order", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({userId: user.id}),
                });

                const data = await response.json();

                // Nếu muốn return kết quả
                return data;
            } catch (error) {
                console.error("Lỗi khi kiểm tra trạng thái đơn hàng:", error);
                return {success: false, error: error.message};
            }
        }
        return {success: false, message: "Không có thông tin người dùng"};
    };

    // Vẫn giữ useEffect để tự động kiểm tra khi user thay đổi
    useEffect(() => {
        // Kiểm tra khi component được mount (tức là khi trang được tải)
        checkOrderStatus();

        // Thêm event listener để kiểm tra khi trang được reload
        if (typeof window !== "undefined") {
            const handleReload = () => {
                checkOrderStatus();
            };

            window.addEventListener("load", handleReload);

            // Cleanup function
            return () => {
                window.removeEventListener("load", handleReload);
            };
        }
    }, [user]);

    const updateUser = async (updatedData) => {
        try {
            // Kiểm tra nếu không có token trong state
            if (!token) {
                console.error("Token không tồn tại");
                return {
                    success: false,
                    error: "Không có quyền truy cập. Vui lòng đăng nhập lại.",
                };
            }

            // Gọi API để cập nhật thông tin user
            const response = await fetch(`/api/auth/profile/${user.id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token || cookies.access_token}`,
                },
                body: JSON.stringify(updatedData),
                credentials: "include",
            });

            const data = await response.json();

            if (data.success) {
                // Cập nhật cookie và state với dữ liệu mới
                const newUserData = {...user, ...updatedData};

                // Cập nhật cookie user_data
                setCookie("user_data", JSON.stringify(newUserData), {
                    path: "/",
                    secure: process.env.NODE_ENV === "production",
                    sameSite: "strict",
                });

                // Cập nhật state
                setUser(newUserData);
                return {success: true};
            } else {
                return {success: false, error: data.error || "Cập nhật thất bại"};
            }
        } catch (error) {
            console.error("Error updating user:", error);
            return {
                success: false,
                error: "Cập nhật thất bại. Vui lòng thử lại sau.",
            };
        }
    };
    const updateUser2 = async (dataUpdate) => {
        // Hoang Anh them ham cap nhat user - ham truoc ko linh dong dc
        try {
            if (!token) {
                return {
                    success: false,
                    error: "Không có quyền truy cập. Vui lòng đăng nhập lại.",
                };
            }
            const response = await fetch(process.env.NEXT_PUBLIC_STRAPI_URL + `/api/users/${user.id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token || cookies.access_token}`,
                },
                body: JSON.stringify(dataUpdate),
                credentials: "include",
            });
            debugger

            const data = await response.json();
            if (data) {
                const res = await fetch(
                    `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/users/${user.id}?populate=*`,
                    {
                        method: "GET",
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: `Bearer ${token || cookies.access_token}`,
                        },
                        credentials: "include",
                    }
                );

                const d = await res.json();
                if (d) {
                    const newUserData = {...user, ...d};
                    setCookie("user_data", JSON.stringify(newUserData), {
                        path: "/",
                        secure: process.env.NODE_ENV === "production",
                        sameSite: "strict",
                    });
                    setUser(newUserData);
                    return {success: true};
                }
            }
        } catch (error) {
            console.error("Error updating user:", error);
            return {
                success: false,
                error: "Cập nhật thất bại. Vui lòng thử lại sau.",
            };
        }
    }

    const register = async (email, password) => {
        try {
            // Tạo AbortController với timeout 30 giây
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            const response = await fetch("/api/auth/register", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    email,
                    password,
                }),
                signal: controller.signal,
                credentials: "include",
            });

            clearTimeout(timeoutId);

            const data = await response.json();

            if (!response.ok) {
                console.error("Lỗi đăng ký từ API:", data.error);
                throw new Error(data.error || "Đăng ký thất bại");
            }

            // Cập nhật user state sau khi đăng ký thành công
            if (data.success && data.user) {
                setUser(data.user);
            }

            return data;
        } catch (error) {
            console.error("Chi tiết lỗi đăng ký:", error);
            // Xử lý lỗi AbortError
            if (error.name === "AbortError") {
                return {
                    success: false,
                    error:
                        "Yêu cầu đăng ký bị hủy bỏ hoặc quá thời gian. Vui lòng thử lại.",
                };
            }
            return {
                success: false,
                error: error.message || "Đăng ký thất bại. Vui lòng thử lại sau.",
            };
        }
    };

    const login = async (email, password, rememberMe = false) => {
        try {
            const response = await fetch("/api/auth/login", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    email,
                    password,
                    provider: "local",
                    rememberMe,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || "Đăng nhập thất bại");
            }

            // Cập nhật user state từ response
            // Note: Cookies are already set by the server via Set-Cookie headers
            if (data.success && data.user) {
                setUser(data.user);
            }

            // Kiểm tra thông tin cá nhân của người dùng
            const needsProfileCompletion = data.user && !data.user.fullname;

            return {
                ...data,
                needsProfileCompletion, // Thêm trạng thái này để kiểm tra xem người dùng có cần bổ sung thông tin không
            };
        } catch (error) {
            console.error("Login error:", error);
            // Xử lý lỗi AbortError
            if (error.name === "AbortError") {
                return {
                    success: false,
                    error:
                        "Yêu cầu đăng nhập bị hủy bỏ hoặc quá thời gian. Vui lòng thử lại.",
                };
            }
            return {
                success: false,
                error: error.message || "Đăng nhập thất bại. Vui lòng thử lại sau.",
            };
        }
    };

    const loginWithGoogle = async (googleCredential) => {
        try {
            const decoded =
                typeof googleCredential === "object" && googleCredential.credential
                    ? googleCredential
                    : {credential: googleCredential};

            const response = await fetch("/api/auth/login", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    email: decoded.email,
                    provider: "google",
                    provider_id: decoded.sub,
                    googleToken: decoded.credential,
                    rememberMe: true,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                console.error("Lỗi đăng nhập Google từ API:", data.error);
                throw new Error(data.error || "Đăng nhập Google thất bại");
            }

            // Cập nhật user state sau khi đăng nhập thành công
            if (data.success && data.user) {
                setUser(data.user);

                // Kiểm tra thông tin cá nhân của người dùng
                const needsProfileCompletion = !data.user.fullname;

                return {
                    ...data,
                    needsProfileCompletion,
                };
            }

            return data;
        } catch (error) {
            console.error("Chi tiết lỗi đăng nhập Google:", error);
            if (error.name === "AbortError") {
                return {
                    success: false,
                    error:
                        "Yêu cầu đăng nhập Google bị hủy bỏ hoặc quá thời gian. Vui lòng thử lại.",
                };
            }
            return {
                success: false,
                error:
                    error.message || "Đăng nhập Google thất bại. Vui lòng thử lại sau.",
            };
        }
    };

    const logout = async (showConfirmation = true) => {
        // Show confirmation dialog if requested
        if (showConfirmation) {
            return Promise.resolve({
                needsConfirmation: true,
                success: false // Indicate that logout hasn't completed yet
            });
        }

        setLogoutLoading(true);

        try {
            // Call logout API
            await fetch("/api/auth/logout", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${cookies.access_token}`,
                },
            });

            // Clear state
            setUser(null);
            setCompletedOrderInfo(null);
            setToken(null);

            // Note: Cookies are already cleared by the server via Set-Cookie headers

            // Show success message
            toast.success("Đăng xuất thành công", "Hẹn gặp lại bạn!");

            // Redirect to login page instead of homepage
            router.push("/dang-nhap");

            return {success: true};
        } catch (error) {
            console.error("Lỗi đăng xuất:", error);

            // Show error message
            toast.error("Có lỗi xảy ra khi đăng xuất", "Vui lòng thử lại");

            return {
                success: false,
                error: "Đăng xuất thất bại. Vui lòng thử lại sau.",
            };
        } finally {
            setLogoutLoading(false);
        }
    };

    const sendOTP = async (email) => {
        try {
            const response = await fetch("/api/auth/send-otp", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token || cookies.access_token}`,
                },
                body: JSON.stringify({email}),
                credentials: "include",
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || "Gửi mã OTP thất bại");
            }

            return data;
        } catch (error) {
            console.error("Send OTP error:", error);
            return {
                success: false,
                error: error.message || "Gửi mã OTP thất bại. Vui lòng thử lại sau.",
            };
        }
    };

    const verifyOTP = async (email, code) => {
        try {
            const response = await fetch("/api/auth/verify-otp", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${cookies.access_token}`,
                },
                body: JSON.stringify({email, code}),
                credentials: "include",
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || "Xác thực mã OTP thất bại");
            }

            return data;
        } catch (error) {
            console.error("Verify OTP error:", error);
            return {
                success: false,
                error:
                    error.message || "Xác thực mã OTP thất bại. Vui lòng thử lại sau.",
            };
        }
    };

    return (
        <UserContext.Provider
            value={{
                user,
                loading,
                logoutLoading,
                completedOrderInfo,
                updateUser,
                logout,
                login,
                loginWithGoogle,
                register,
                sendOTP,
                verifyOTP,
                token,
                isAuthenticated,
                checkOrderStatus,
                updateUser2
            }}
        >
            {children}
        </UserContext.Provider>
    );
};
